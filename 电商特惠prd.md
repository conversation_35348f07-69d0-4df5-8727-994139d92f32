## FEATURE:

[描述要开发的Java功能 - 具体说明功能需求、业务场景和技术要求]
1、支持主产品传入【电商退货】ed-m-0076；

2、修改：当用户业务身份为快递C2C时，主产品=【电商退货】时，揽收前，不支持修改收货信息中的一、二、三级地址编码&名称，即省、市、县编码&名称；揽收后允许修改；

3、询价写台账：【电商退货】主产品写pos台账的WaybillSign为0，不支持E卡支付；

4、数据同步主产品：外单wbs31位新增枚举；waybillsign 31=【N】

5、接单消息：快递C2C接单消息中。订单接单成功结果消息通知，增加attachmentInfos
## EXAMPLES:

[说明`core/examples/`文件夹中的相关示例，解释如何参考这些代码模式和最佳实践]

## DOCUMENTATION:

[列出开发过程中需要参考的文档资源：
- API文档链接
- 第三方库文档
- 企业内部文档
- 相关技术规范]

### 功能提示词

#### 1. 新主产品支持与数据同步

##### 功能描述
为OMS系统添加新的主产品类型支持，包括产品枚举定义、外单标识映射和数据同步机制。

##### 实现要点
- **产品枚举扩展**: 在`ProductEnum`中添加新产品类型
- **外单映射配置**: 在`WaybillInfoMappingUtil`中添加产品标识映射逻辑
- **数据同步机制**: 确保新产品在各个数据流转环节的正确识别

##### 关键代码模式
```java
// 1. 产品枚举定义
NEW_PRODUCT("ed-m-xxxx", "产品名称", 标识位置, "标识值"),

// 2. 外单映射逻辑
if (ProductEnum.NEW_PRODUCT.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
    mainProduct.setProductName(ProductEnum.NEW_PRODUCT.getDesc());
    mainProduct.setProductNo(ProductEnum.NEW_PRODUCT.getCode());
}

// 3. 静态注册表更新（自动处理）
private static final Map<String, ProductEnum> registry = new HashMap();
static {
    Iterator iterator = EnumSet.allOf(ProductEnum.class).iterator();
    while (iterator.hasNext()) {
        ProductEnum typeEnum = (ProductEnum) iterator.next();
        registry.put(typeEnum.getCode(), typeEnum);
    }
}
```

##### 涉及文件
- `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`
- `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`

#### 2. 修改项识别方法

##### 功能描述
实现订单修改项的精确识别。

##### 实现要点
- **变更检测方法**: 在`ChangedPropertyDelegate`中实现各种变更检测方法
- **字段级变更**: 提供细粒度的字段变更检测能力

##### 关键代码模式
```java

// 通用变更检测模式
public boolean specificFieldHaveChange() {
    if (changedPropertyMap == null) {
        return false;
    }
    return changedPropertyMap.get(ModifyItemConfigEnum.SPECIFIC_FIELD.getCode()) != null;
}
```

##### 涉及文件
- `jdl-oms-express-domain-model/src/main/java/cn/jdl/oms/express/domain/vo/modify/ChangedPropertyDelegate.java`

#### 3. 修改规则守则

##### 功能描述
基于产品类型和订单状态实现修改权限控制，确保业务规则的正确执行。

##### 实现要点
- **状态检查**: 基于订单状态（如揽收前后）进行修改权限判断
- **产品类型限制**: 针对特定产品类型实施修改限制
- **异常处理**: 提供清晰的业务异常信息和错误提示

##### 关键代码模式
```java

// 通用业务规则检查模式
private void validateBusinessRule(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
    // 1. 状态检查
    if (!isValidStatus(orderModel.getOrderSnapshot().getOrderStatus())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前状态不允许修改");
    }
    
    // 2. 产品类型检查
    if (isRestrictedProduct(orderModel.getProductDelegate().getMajorProductNo())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前产品类型不允许修改");
    }
    
    // 3. 字段变更检查
    if (hasRestrictedFieldChange(changedPropertyDelegate)) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("不允许修改限制字段");
    }
}
```

##### 涉及文件
- `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`
- `jdl-oms-express-b2c-extension/src/main/java/cn/jdl/oms/express/b2c/extension/white/B2CModifyWhiteExtension.java`

#### 4. 支付方式限制

##### 功能描述
基于产品类型实现支付方式的限制，如禁用E卡支付等。

##### 实现要点
- **支付限制枚举**: 在`ECardDisableReasonEnum`中定义支付限制原因
- **支付校验逻辑**: 在询价和台账写入过程中进行支付方式校验
- **错误信息提示**: 提供明确的支付限制原因说明

##### 关键代码模式
```java
// 1. 支付限制枚举定义
PRODUCT_PAYMENT_DISABLE("编码", "特定产品不支持特定支付方式"),


// 3. 通用支付限制检查模式
private void validatePaymentMethod(ExpressOrderModel orderModel) {
    String productNo = orderModel.getProductDelegate().getMajorProductNo();
    
    // 检查产品是否支持E卡支付
    if (isECardRestrictedProduct(productNo)) {
        throw new BusinessDomainException(PAYMENT_ERROR_CODE)
            .withCustom(ECardDisableReasonEnum.PRODUCT_PAYMENT_DISABLE.getDesc());
    }
}
```

##### 涉及文件
- `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`
- 各种OrderBankFacadeTranslator类

#### 5. 接单消息字段扩展

##### 功能描述
在接单成功消息通知中增加新的字段信息，支持附件信息等扩展数据的传递。

##### 实现要点
- **消息结构扩展**: 在接单消息DTO中添加新字段
- **数据转换逻辑**: 在消息转换器中处理新字段的映射
- **向下兼容**: 确保新字段的添加不影响现有消息处理逻辑

##### 关键代码模式
```java
// 1. 消息字段扩展
private List<AttachmentInfo> attachmentInfos;

// 2. 消息转换逻辑
Optional.ofNullable(source.getAttachmentInfos()).ifPresent(attachmentInfos -> {
    List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream()
        .map(this::convertAttachmentInfo)
        .collect(Collectors.toList());
    target.setAttachmentInfos(attachmentInfoDtos);
});

// 3. 通用字段转换模式
```

##### 涉及文件
- `jdl-oms-express-client-model/src/main/java/cn/jdl/oms/express/model/CreateExpressOrderRequest.java`
- 各种Translator类

#### 通用开发模式总结

##### 1. 枚举扩展模式
```java
// 步骤1: 添加枚举值
NEW_ENUM_VALUE("code", "description", additionalParams),

// 步骤2: 确保静态注册表自动更新
// 步骤3: 在相关业务逻辑中使用新枚举值
```

##### 2. 扩展点模式
```java
@Override
public void execute(ExpressOrderContext context) throws AbilityExtensionException {
    CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute",
            UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE,
            UmpKeyConstants.METHOD_ENABLE_HEART,
            UmpKeyConstants.METHOD_ENABLE_TP);
    try {
        // 业务逻辑实现
    } catch (Exception e) {
        Profiler.functionError(callerInfo);
        LOGGER.error("扩展点执行异常", e);
        throw new AbilityExtensionException("扩展点执行失败");
    } finally {
        Profiler.registerInfoEnd(callerInfo);
    }
}
```

#####3. 数据转换模式
```java
// 使用Optional处理空值
Optional.ofNullable(source.getField()).ifPresent(field -> {
    target.setField(convertField(field));
});

// 集合转换
List<TargetDto> targetList = sourceList.stream()
    .map(this::convertItem)
    .collect(Collectors.toList());
```

##### 4. 业务校验模式
```java
// 统一异常处理
if (!isValid(condition)) {
    throw new BusinessDomainException(UnifiedErrorSpec.Category.ERROR_TYPE)
        .withCustom("具体错误信息");
}

// 日志记录
LOGGER.error("业务校验失败, orderNo: {}, reason: {}", orderNo, reason);

## OTHER CONSIDERATIONS:

[其他考虑因素和特殊要求：
- 现有系统集成约束
- 性能要求
- 安全要求
- 企业中间件使用偏好
- AI开发时容易遗漏的要点]

## MIDDLEWARE HINTS:

[如果功能涉及特定中间件，可以提供关键词提示：
- 需要远程调用服务 → 会匹配JSF
- 需要消息队列处理 → 会匹配JMQ
- 需要缓存功能 → 会匹配JIMDB
- 需要配置管理 → 会匹配DUCC
- 需要日志记录 → 会匹配DongLog]