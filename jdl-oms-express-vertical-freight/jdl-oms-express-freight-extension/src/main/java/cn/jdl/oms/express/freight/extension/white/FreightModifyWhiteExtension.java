package cn.jdl.oms.express.freight.extension.white;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.white.IWhiteExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.waybill.FreightWayBillQueryFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WayBillQueryResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.FinanceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.FreightReaddressUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifyVehicleFeeOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TmsEnquiryBillOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.DpDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.utils.ModifyProductUtil;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.domain.vo.modify.ModifyProduct;
import cn.jdl.oms.express.shared.common.constant.FreightFTLConstants;
import cn.jdl.oms.express.domain.infrs.acl.facade.fee.FreightFTLFeeFacade;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ReaddressMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkHolder;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.jd.matrix.sdk.annotation.Extension;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 快运修改白名单
 */
@Extension(code = ExpressOrderProduct.CODE)
public class FreightModifyWhiteExtension implements IWhiteExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightModifyWhiteExtension.class);

    /**
     * 快运运单详情查询
     */
    @Resource
    private FreightWayBillQueryFacade freightWayBillQueryFacade;

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 快运整车直达费用处理
     */
    @Resource
    private FreightFTLFeeFacade freightFTLFeeFacade;

    /**
     * 修改标记-已修改
     */
    private static final String MODIFY_MARK_YES = "1";

    /**
     * 财务域工具类
     */
    @Resource
    private FinanceUtil financeUtil;

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    // 政府补贴订单状态常量
    private static final String GOV_SUBSIDY_INITIATE_RESHOOT_STATUS = "4";  // 发起补拍状态
    private static final String GOV_SUBSIDY_MERCHANT_APPROVED_STATUS = "7"; // 商家审核通过状态

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();

        // 更改的属性集合
        ChangedPropertyDelegate changedPropertyDelegate = expressOrderContext.getChangedPropertyDelegate();

        if (ModifySceneRuleUtil.isNoTaskFinishCollect(orderModel)) {
            //无任务揽收不执行扩展点校验，统一在ability校验
            return;
        }

        // 获取修改策略
        String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(orderModel);

        // 修改公共的校验
        validateCommonProperty(changedPropertyDelegate, orderModel, modifySceneRule, UnitedBusinessIdentityUtil.isUnitedIdentity(expressOrderContext));

        // 原单标记位
        ModifyMarkHolder modifyMarkHolder = new ModifyMarkHolder(getModifyMark(orderModel));

        // 港澳修改
        // 如果是港澳订单
        if (orderModel.getOrderSnapshot().isHKMO()) {
            validateHKMO(changedPropertyDelegate, orderModel);
        }

        // 揽收前修改
        if (ModifySceneRuleUtil.isBeforePickUp(modifySceneRule)) {
            validateBeforePickUp(modifyMarkHolder, modifySceneRule, changedPropertyDelegate, orderModel, UnitedBusinessIdentityUtil.isUnitedIdentity(expressOrderContext));
        }

        // 揽收后修改
        if (ModifySceneRuleUtil.isAfterPickUp(modifySceneRule)) {
            validateAfterPickUp(modifyMarkHolder, modifySceneRule, changedPropertyDelegate, orderModel);
        }

        // 揽收后改址
        if (ModifySceneRuleUtil.isAfterPickUpReaddress(modifySceneRule)) {
            validateAfterPickUpReaddress(modifyMarkHolder, modifySceneRule, changedPropertyDelegate, orderModel);
        }

        // 仅修改收件人联系方式
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            validateOnlyModifyConsigneeContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);
        }

        // 修改联系方式
        if (ModifySceneRuleUtil.isModifyContactInformation(modifySceneRule)) {
            validateModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);
        }

        // 销售修改
        if (ModifySceneRuleUtil.isModifyBySalesperson(modifySceneRule)) {
            validateModifyBySalesperson(modifyMarkHolder, changedPropertyDelegate, expressOrderContext);
        }

        // 新增压车费
        if (ModifySceneRuleUtil.isInsertExtrudeFee(modifySceneRule)) {
            validateInsertExtrudeFee(modifyMarkHolder, changedPropertyDelegate, orderModel);
        }

        // 营业厅修改
        if (ModifySceneRuleUtil.isModifyByBusinessHall(modifySceneRule)) {
            validateModifyByBusinessHall(modifyMarkHolder, changedPropertyDelegate, expressOrderContext);
        }

        // 仓出库发货
        if (ModifySceneRuleUtil.isOutboundDelivery(modifySceneRule)) {
            validateOutboundDelivery(orderModel);
        }

        // 特殊修改策略仅修改货品
        if (ModifySceneRuleUtil.isSpecialModify(modifySceneRule)){
            specialModifyValid(changedPropertyDelegate);
        }

        // 仅修改补签标识
        if (ModifySceneRuleUtil.isModifyReSignFlag(modifySceneRule)) {
            validateModifyReSignFlag(changedPropertyDelegate);
        }

        // 仅修改国补审核状态
        if (ModifySceneRuleUtil.isModifyGuobuStatus(modifySceneRule)) {
            validateModifyGovSubsidyApprovalStatus(orderModel, orderModel.getOrderSnapshot(), changedPropertyDelegate);
        }

        // 更新标位
        String modifyMark = modifyMarkHolder.modifyMark();
        changedPropertyDelegate.setModifyMark(modifyMark);
        orderModel.putAttachment(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);

        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        if (CollectionUtils.isNotEmpty(snapshot.getRefOrderInfoDelegate().getServiceEnquiryOrderNos())) {
            // 原单存在服务单，确认是否需要取消服务单
            if (orderModel.needCancelServiceOrder()) {
                // 查询服务单详情 -- 服务单已支付则不允许
                GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
                facadeRequest.setOrderNo(snapshot.getRefOrderInfoDelegate().getServiceEnquiryOrderNos().get(0));
                GetOrderFacadeResponse facadeResponse = getOrderFacade.getOrder(orderModel.requestProfile(), facadeRequest);
                if (PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(facadeResponse.getFinance().getPaymentStatus())) {
                    LOGGER.error("支付完成不允许取消服务询价单");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                            .withCustom("支付完成不允许取消服务询价单");
                }
            }
        }
    }

    /**
     * 修改公共的校验
     * 寄件人地址不允许修改
     * 妥投、拒收、已取消不允许修改
     */
    private void validateCommonProperty(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel, String modifySceneRule, boolean isUnitedIdentity) {
        // 订单当前状态
        OrderStatusEnum currentOrderStatus = orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus();

        // 寄件人地址不允许修改
        if (changedPropertyDelegate.consignorAddressHaveChange()) {
            // 允许改发货人地址：揽收前修改 并且 特殊SystemSubCaller
            boolean allowModifyConsignorAddress = allowModifyConsignorAddress(modifySceneRule, orderModel);
            // 融合身份不校验。特殊SystemSubCaller揽收前修改不校验。
            if (isUnitedIdentity
                    || UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                    || allowModifyConsignorAddress) {
                LOGGER.info("特殊场景放开卡控：寄件人地址不允许修改");
            } else {
                LOGGER.info("寄件人地址不允许修改. orderNo: {}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("寄件人地址不允许修改");
            }
        }

        // 送装分离产品校验
        checkP0082(orderModel);

        //特殊修改，终态下允许修改
        if(ModifySceneRuleUtil.isSpecialModify(modifySceneRule)){
            return;
        }

        //碳排放计算修改，终态下允许修改产品
        if(ModifySceneRuleUtil.isCarbonEmissionCalculation(modifySceneRule) && changedPropertyDelegate.productHaveChange()) {
            LOGGER.info("妥投、拒收、已取消状态下修改修改碳排放碳减排");
            return;
        }

        // 仅修改补签标识，终态下允许修改
        if( ModifySceneRuleUtil.isModifyReSignFlag(modifySceneRule)){
            return;
        }

        // 仅修改订单标识，终态下允许修改
        if (ModifySceneRuleUtil.isOnlyModifyOrder(modifySceneRule)) {
            return;
        }

        // 仅修改国补审核状态，终态下允许修改
        if (ModifySceneRuleUtil.isModifyGuobuStatus(modifySceneRule)) {
            return;
        }

        if (ModifySceneRuleUtil.isOutboundDelivery(orderModel)) {
            LOGGER.info("仓出库发货场景，不校验订单状态");
        } else {
            // 妥投、拒收、已取消不允许修改
            if (OrderStatusEnum.CUSTOMER_SIGNED.getCode().equals(currentOrderStatus.getCode())
                    || OrderStatusEnum.CUSTOMER_REJECTED.getCode().equals(currentOrderStatus.getCode())
                    || OrderStatusEnum.CANCELED.getCode().equals(currentOrderStatus.getCode())) {
                LOGGER.info("妥投、拒收、已取消状态下不允许修改: {}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("妥投、拒收、已取消状态下不允许修改");
            }
        }

    }

    /**
     * 送装分离产品校验
     *
     * @param orderModel
     */
    private void checkP0082(ExpressOrderModel orderModel) {

        if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.SERVICE_PLUS_SWITCH)) {
            return;
        }

        // 送装分离增值服务揽收后不能新增或删除
        if (!orderModel.getOrderSnapshot().getOrderStatus().isBeforePickedUp()) {
            Product p0082 = orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.FREIGHT_A_0082.getCode());
            if (p0082 != null && (OperateTypeEnum.INSERT == p0082.getOperateType() || OperateTypeEnum.DELETE == p0082.getOperateType())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("送装分离增值服务揽收后不能新增或删除");
            }
        }

        // 送装分离增值服务产品要素校验
        if (productAttrsHasModified(orderModel, AddOnProductEnum.FREIGHT_A_0082.getCode())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("送装分离增值服务产品要素不支持修改");
        }

    }

    /**
     * 判断产品要素是否修改。只考虑 OperateTypeEnum.UPDATE 的情况
     *
     * @param orderModel
     * @param productNo
     * @return
     */
    private boolean productAttrsHasModified(ExpressOrderModel orderModel, String productNo) {

        Product product = orderModel.getProductDelegate().ofProductNo(productNo);
        // 若当前产品为空 或 其他INSERT、DELETE操作，产品要素当做未变化处理。
        if (product != null && OperateTypeEnum.UPDATE == product.getOperateType()) {
            // 送装分离产品要素揽收前揽收后均不支持修改。若主产品变化，所有产品均会被标记为UPDATE，需要继续判断产品要素的值
            if (orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getProductDelegate() != null) {
                Product productSnapshot = orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(productNo);
                // 若快照产品为空，产品要素当做发生变化处理。
                if (productSnapshot == null) {
                    return true;
                }
                return isModifiedProductMap(product.getProductAttrs(), productSnapshot.getProductAttrs());
            }
            // 若快照为空，产品要素当做发生变化处理。
            return true;
        }

        return false;
    }

    /**
     * 判断产品信息的产品要素或者扩展字段是否变化
     */
    private boolean isModifiedProductMap(Map<String, String> map, Map<String, String> originMap) {
        if (isEmptyMap(map) && isEmptyMap(originMap)) {
            return false;
        }
        if (isEmptyMap(map) || isEmptyMap(originMap)) {
            return true;
        }
        int newMapSize = map.size();
        int originMapSize = originMap.size();

        if (map.get(AttachmentKeyEnum.OPERATE_TYPE.getKey()) != null) {
            newMapSize = newMapSize - 1;
        }
        if (originMap.get(AttachmentKeyEnum.OPERATE_TYPE.getKey()) != null) {
            originMapSize = originMapSize - 1;
        }
        if (newMapSize != originMapSize) {
            return true;
        }

        // key对比不可忽略，避免后续对比方法，map1={key1=null},map2={key2=null}对比不出来
        HashSet<String> keySet = new HashSet<>(map.keySet());
        HashSet<String> originKeySet = new HashSet<>(originMap.keySet());
        // 排除operateType操作类型
        keySet.remove(AttachmentKeyEnum.OPERATE_TYPE.getKey());
        originKeySet.remove(AttachmentKeyEnum.OPERATE_TYPE.getKey());
        if (!keySet.equals(originKeySet)) {
            return true;
        }

        for (String key : map.keySet()) {
            if (AttachmentKeyEnum.OPERATE_TYPE.getKey().equals(key)) {
                continue;
            }
            String value = map.get(key);
            String originValue = originMap.get(key);
            if (!Objects.equals(value, originValue)) {
                // todo 有需求时再更进一步对比（value为集合类序列化时，可能顺序不同但实际相等）
                return true;
            }
        }

        return false;
    }

    /**
     * 是否空Map
     *
     * @param map
     * @return
     */
    private boolean isEmptyMap(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return true;
        }
        //TODO 若上游传了operateType = null，需要特殊处理
        if (map.size() == 1 && null != map.get(AttachmentKeyEnum.OPERATE_TYPE.getKey())) {
            return true;
        }
        return false;
    }

    /**
     * 揽收前修改：只有发货人地址、开票类型不能修改
     */
    private void validateBeforePickUp(ModifyMarkHolder modifyMarkHolder, String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel, boolean isUnitedIdentity) {
        // 发货人地址不允许修改
        if (changedPropertyDelegate.consignorAddressHaveChange()) {
            // 允许改发货人地址：揽收前修改 并且 特殊SystemSubCaller
            boolean allowModifyConsignorAddress = allowModifyConsignorAddress(modifySceneRule, orderModel);
            // 融合身份不校验。特殊SystemSubCaller揽收前修改不校验。
            if (isUnitedIdentity
                    || UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                    || allowModifyConsignorAddress) {
                LOGGER.info("特殊场景放开卡控：发货人地址不允许修改");
            } else {
                LOGGER.info("发货人地址不允许修改，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("发货人地址不允许修改");
            }
        }
        // 开票类型不允许修改
        if (changedPropertyDelegate.invoiceTypeChange()) {
            LOGGER.info("开票类型不允许修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改开票类型");
        }

        // 判断折后金额是否大于零
        boolean isDiscountAmountGreaterThanZero = isDiscountAmountGreaterThanZero(orderModel);
        LOGGER.info("揽收前修改，折后金额是否大于零：" + isDiscountAmountGreaterThanZero);

        // 揽收前修改，校验结算方式能否修改
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.FREIGHT_MODIFY_CHECK_SETTLEMENT_TYPE_SWITCH)) {
            LOGGER.info("快运修改校验结算方式变更切换开关:开启");
            validateBeforePickUpSettlementType(changedPropertyDelegate, orderModel, isDiscountAmountGreaterThanZero);
        } else {
            LOGGER.info("快运修改校验结算方式变更切换开关:关闭");
        }


        // 揽收前修改，校验用户能否修改订单信息
        validateBeforePickUpInitiatorType(orderModel, isDiscountAmountGreaterThanZero);

        // 揽收前修改，校验校验增值产品
        validateProductBeforePickUp(changedPropertyDelegate, orderModel);
    }

    /**
     * 揽收后修改：到付现结/寄付月结可以修改增值服务，寄付现结只能改快运改址；同城同站可以改收货人地址
     */
    private void validateAfterPickUp(ModifyMarkHolder modifyMarkHolder, String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 校验揽收后都不能修改的项目
        validateCommonPropertyAfterPickUp(changedPropertyDelegate, orderModel, modifySceneRule);

        // 同城同站修改校验：只能改第4级地址、详细地址、收件人信息；同时校验是否存在不允许改址的产品
        validateSameSiteReaddressConsignee(changedPropertyDelegate, orderModel);

        // 寄付现结揽收后修改：只能新增快运改址、先到先送
        if (isCashOnPick(orderModel)) {
            cashOnPickValidateProduct(modifyMarkHolder, changedPropertyDelegate, orderModel);
        } else {
            // 校验增值产品，并更新标位
            validateProduct(modifyMarkHolder, changedPropertyDelegate, orderModel);
        }
    }

    /**
     * 揽收后改址：只能改收货人地址
     */
    private void validateAfterPickUpReaddress(ModifyMarkHolder modifyMarkHolder, String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 校验揽收后都不能修改的项目
        validateCommonPropertyAfterPickUp(changedPropertyDelegate, orderModel, modifySceneRule);

        //校验揽收后改址的取消
        validateReaddressSystemCaller(orderModel);

        // 校验揽收后改址的收货信息；同时校验是否存在不允许改址的产品
        validateReaddressConsignee(modifyMarkHolder, changedPropertyDelegate, orderModel);

        // 按结算方式设置改址状态，并更新标位
        setReaddressStatusBySettlementType(modifyMarkHolder, orderModel);

        // 拦截状态校验
        interceptStatusValid(orderModel);

        // 拦截一单到底不支持同时修改COD
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.FREIGHT_JF_INTERCEPT_THROUGH_ORDER_MODIFY_COD_VALID_SWITCH)) {
            if (orderModel.isFreightJFInterceptionThroughOrderModify()) {
                if(changedPropertyDelegate.productHaveChange()
                        && codAmountIsUpdate(orderModel)){
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("寄付订单含COD，不支持拦截一单到底");
                }
            }
        }
    }

    /**
     * 改址一单到底-拦截状态校验
     * @param orderModel 入参订单
     */
    private void interceptStatusValid(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        InterceptTypeEnum interceptType = orderSnapshot.getInterceptType();
        if(null != interceptType && !orderModel.isInterceptionThroughOrderModify()){
            // 原单拦截状态：原始订单状态【拦截中】、【拦截成功】不允许改址
            List<String> readdressOrderInterceptTypeWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.FREIGHT_READDRESS_ORDER_INTERCEPT_TYPE_BLACK_LIST, ",");
            if (readdressOrderInterceptTypeWhiteList.contains(String.valueOf(interceptType.getCode()))) {
                String forbidReaddressDesc = "原单拦截状态为:" + interceptType.getDesc() + ",不允许改址";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }
        }
    }

    /**
     * 校验揽收后都不能修改的项目
     */
    private void validateCommonPropertyAfterPickUp(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel, String modifySceneRule) {
        if (changedPropertyDelegate.consignorHaveChange()) {
            LOGGER.info("不允许修改发货信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改发货信息");
        }
        if (changedPropertyDelegate.cargoHaveChange()) {
            LOGGER.info("不允许修改货品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改货品信息");
        }
        if (changedPropertyDelegate.goodsHaveChange()) {
            LOGGER.info("不允许修改商品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改商品信息");
        }
        if (changedPropertyDelegate.financeHaveChangeNotAllowAfterPickup() && !orderModel.isInterceptionThroughOrderModify()) {
            LOGGER.info("不允许修改交易费用信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改交易费用信息");
        }
        if (ModifySceneRuleUtil.isAfterPickUp(modifySceneRule)) {
            // 揽收后修改，配送信息中的物流服务要求、预计送达时间可以改
            Set<ModifyItemConfigEnum> ignoreModifyItemConfigEnumSet = new HashSet<>();
            ignoreModifyItemConfigEnumSet.add(ModifyItemConfigEnum.PLAN_DELIVERY_TIME);
            ignoreModifyItemConfigEnumSet.add(ModifyItemConfigEnum.SERVICE_REQUIREMENTS);
            // 京东转德邦，可以修改派送站点
            // todo 下次上线后去除开关
            if (orderModel.getOrderSnapshot().isJDLToDP()) {
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.JDL_TO_DP_SWITCH)) {
                    LOGGER.info("jdlToDpSwitch=true，快运修改白名单走新流程");
                    ignoreModifyItemConfigEnumSet.add(ModifyItemConfigEnum.END_STATION_NO);
                } else {
                    LOGGER.info("jdlToDpSwitch=flase，快运修改白名单走旧流程");
                }
            }
            if (changedPropertyDelegate.shipmentHaveChangeIgnoreSomething(ignoreModifyItemConfigEnumSet)) {
                LOGGER.info("（忽略对比项目={}）不允许修改配送信息，orderNo:{}", ignoreModifyItemConfigEnumSet, orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改配送信息");
            }
        } else {
            // 其他修改策略，配送信息都不能改
            if (changedPropertyDelegate.shipmentHaveChange()) {
                LOGGER.info("不允许修改配送信息，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改配送信息");
            }
        }
        if (changedPropertyDelegate.promotionHaveChange()) {
            LOGGER.info("不允许修改营销信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改营销信息");
        }
        if (changedPropertyDelegate.invoiceTypeChange()) {
            LOGGER.info("不允许修改开票类型，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不允许修改开票类型");
        }

        // 改址状态为改址中，不能修改
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null) {
            LOGGER.info("订单快照为空，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("订单快照为空");
        }
        if (FreightReaddressUtil.isModifying(orderSnapshot)) {
            LOGGER.info("改址状态为改址中，不能修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("改址状态为改址中，不能修改");
        }
    }

    /**
     * 同城同站修改校验
     * 只能改第4级地址、详细地址、收件人信息
     */
    private void validateSameSiteReaddressConsignee(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 收件人地址未发生变化，不进一步校验
        if (changedPropertyDelegate == null
                || !changedPropertyDelegate.consigneeAddressHaveChange()) {
            return;
        }

        // 同城同站校验由接入层卡，订单中心不校验省、市、区不允许修改

        // 必须要有增值服务快运改址
        if (!hasAddOnProductFreightReaddress(orderModel)) {
            LOGGER.error("订单{} 没有增值服务快运改址，不允许揽收后改址", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom(String.format("订单%s 没有增值服务快运改址，不允许揽收后改址", orderModel.orderNo()));
        }

        // 校验是否存在不允许改址的产品
        validateReaddressProductBlacklist(orderModel, changedPropertyDelegate);
    }

    /**
     * 校验揽收后改址的收货信息
     */
    private void validateReaddressConsignee(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 不允许未修改收货人地址
        if (changedPropertyDelegate == null
                || !changedPropertyDelegate.consigneeAddressHaveChange()) {
            LOGGER.info("收货人地址未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("收货人地址未发生变化");
        }

        // 根据原单标位判断是否修改过
        String originSign = modifyMarkHolder.getPositionMark(ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP);
        if (ReaddressMarkEnum.RESULT_SUCCESS.getCode().equals(originSign)
                || ReaddressMarkEnum.APPLY_SUCCESS.getCode().equals(originSign)) {
            LOGGER.info("地址已经修改，不支持再次修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("地址已经修改，不支持再次修改");
        }

        // 必须要有增值服务快运改址
        if (!hasAddOnProductFreightReaddress(orderModel)) {
            LOGGER.error("订单{} 没有增值服务快运改址，不允许揽收后改址", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom(String.format("订单%s 没有增值服务快运改址，不允许揽收后改址", orderModel.orderNo()));
        }

        // 校验是否存在不允许改址的产品
        validateReaddressProductBlacklist(orderModel, changedPropertyDelegate);
    }

    /**
     * 按结算方式设置改址状态
     */
    private void setReaddressStatusBySettlementType(ModifyMarkHolder modifyMarkHolder, ExpressOrderModel orderModel) {
        // 改址标位默认成功
        ReaddressMarkEnum readdressMarkEnum = ReaddressMarkEnum.RESULT_SUCCESS;

        // 原单是到付现结：改址成功。后续直接下发OFC
        if (isCashOnDelivery(orderModel)) {
            orderModel.setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFY_SUCCESS);
        }
        // 原单是寄付月结：改址成功。后续直接下发OFC
        if (isMonthlyPayment(orderModel)) {
            orderModel.setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFY_SUCCESS);
        }
        // 原单是寄付现结：默认：改址中，后续需要监听支付超时，支付超时变成改址失败；支付成功变成改址成功，并下发OFC。写台账多退少补后可能改为改址成功，直接下发OFC。
        if (isCashOnPick(orderModel)) {
            orderModel.setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFYING);
            readdressMarkEnum = ReaddressMarkEnum.APPLY_SUCCESS;
        }
        // todo 拒收一单到底 二期快运
        // 拦截一单到底：改址成功 即使是寄付先款也直接按成功处理持久化下发，未支付由终端妥投时卡控
        if (orderModel.isFreightJFInterceptionThroughOrderModify()) {
            orderModel.setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFY_SUCCESS);
        }

        // 记录更新标位
        modifyMarkHolder.addUpdateMarkRecord(ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP, readdressMarkEnum.getCode());
    }

    /**
     * 产品校验
     */
    private void validateProduct(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 获取修改后的产品
        List<ModifyProduct> modifyProductList = ModifyProductUtil.getModifyProduct(changedPropertyDelegate);
        if (CollectionUtils.isEmpty(modifyProductList)) {
            LOGGER.info("产品未发生变化，orderNo:{}", orderModel.orderNo());
            return;
        }

        // 只有特定产品允许修改
        validateProductModify(orderModel, modifyProductList);

        // 校验有修改的商品能否新增、删除、更新
        validateProductInsertOrDelete(orderModel, modifyProductList);

        // 校验代收货款
        validateCod(orderModel, modifyMarkHolder, modifyProductList, changedPropertyDelegate);
    }

    /**
     * 揽收前修改，校验校验增值产品
     */
    private void validateProductBeforePickUp(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 获取修改后的产品
        List<ModifyProduct> modifyProductList = ModifyProductUtil.getModifyProduct(changedPropertyDelegate);
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        // 校验有修改的商品能否新增或者删除
        validateProductInsertOrDeleteBeforePickUp(orderModel, modifyProductList);
    }

    /**
     * 寄付现结揽收后修改：只能新增快运改址、先到先送、修改等通知派送的产品要素
     */
    private void cashOnPickValidateProduct(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 获取修改后的产品
        List<ModifyProduct> modifyProductList = ModifyProductUtil.getModifyProduct(changedPropertyDelegate);
        if (CollectionUtils.isEmpty(modifyProductList)) {
            LOGGER.info("产品未发生变化，orderNo:{}", orderModel.orderNo());
            return;
        }

        // 只有特定产品允许修改
        cashOnPickValidateProductModify(orderModel, modifyProductList);

        // 校验有修改的产品能否新增、删除、更新
        validateProductInsertOrDelete(orderModel, modifyProductList);
    }

    /**
     * 代收货款校验
     */
    private void validateCod(ExpressOrderModel orderModel, ModifyMarkHolder modifyMarkHolder, List<ModifyProduct> modifyProductList, ChangedPropertyDelegate changedPropertyDelegate) {
        if (DpDeliveryOrderSignUtil.flag(orderModel)) {
            LOGGER.info("德邦落地配，不卡控修改代物流公司收货款");
            return;
        }

        // 代收货款无变化不处理
        if (CollectionUtils.isEmpty(modifyProductList)
                || !hasCOD(modifyProductList)) {
            return;
        }

        // 代收货款不支持从无改到有
        Product codProduct = orderModel.getProductDelegate().getCodProduct();
        if (codProduct.getOperateType() == OperateTypeEnum.INSERT) {
            Product originCod = orderModel.getOrderSnapshot().getProductDelegate().getCodProduct();
            if (originCod == null) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("不支持新增代收货款增值服务");
            }
        }

        // 代收货款有变化，打标记录
        modifyMarkHolder.addUpdateMarkRecord(ModifyMarkEnum.COD);
    }

    /**
     * 判断原单是否到付现结
     */
    private boolean isCashOnDelivery(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null
                || orderSnapshot.getFinance() == null
                || orderSnapshot.getFinance().getSettlementType() == null) {
            LOGGER.error("未获取到原单结算方式");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("未获取到原单结算方式");
        }
        return SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(orderSnapshot.getFinance().getSettlementType().getCode());
    }

    /**
     * 判断原单是否寄付现结
     */
    private boolean isCashOnPick(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null
                || orderSnapshot.getFinance() == null
                || orderSnapshot.getFinance().getSettlementType() == null) {
            LOGGER.error("未获取到原单结算方式");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("未获取到原单结算方式");
        }
        return SettlementTypeEnum.CASH_ON_PICK.getCode().equals(orderSnapshot.getFinance().getSettlementType().getCode());
    }

    /**
     * 判断原单是否寄付月结
     */
    private boolean isMonthlyPayment(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null
                || orderSnapshot.getFinance() == null
                || orderSnapshot.getFinance().getSettlementType() == null) {
            LOGGER.error("未获取到原单结算方式");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("未获取到原单结算方式");
        }
        return SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(orderSnapshot.getFinance().getSettlementType().getCode());
    }

    /**
     * 判断原单是否到付月结
     */
    private boolean isMonthlyPaymentDelivery(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null
                || orderSnapshot.getFinance() == null
                || orderSnapshot.getFinance().getSettlementType() == null) {
            LOGGER.error("未获取到原单结算方式");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("未获取到原单结算方式");
        }
        return SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY.getCode().equals(orderSnapshot.getFinance().getSettlementType().getCode());
    }

    /**
     * 获取原单的修改标记
     */
    private String getModifyMark(ExpressOrderModel orderModel) {
        String modifyMark = null;
        if (orderModel.getOrderSnapshot().getExtendProps() != null) {
            modifyMark = orderModel.getOrderSnapshot().getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }
        return modifyMark;
    }

    /**
     * 判断是否有快运改址增值产品
     */
    private boolean hasAddOnProductFreightReaddress(ExpressOrderModel orderModel) {
        // 存在未删除的产品
        if (orderModel != null
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS_FREIGHT.getCode()) != null) {
            Product product = orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS_FREIGHT.getCode());
            return product.getOperateType() == null
                    || OperateTypeEnum.DELETE.getCode().intValue() != product.getOperateType().getCode();
        }
        return false;
    }

    /**
     * 月结、到付现结：校验增值产品能否修改（新增、删除、更新）
     */
    private void validateProductModify(ExpressOrderModel orderModel, List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        // 基本白名单
        Set<String> modifyWhitelist = getAllowedModifiedProductCode(orderModel);

        // 特殊白名单：拦截一单到底
        if (orderModel.isInterceptionThroughOrderModify()) {
            List<String> whiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.FREIGHT_INTERCEPT_READDRESS_AFTER_PICKUP_ALLOW_MODIFY_PRODUCT_WHITE_LIST,",");
            modifyWhitelist.addAll(whiteList);
        }

        // 特殊白名单：德邦一单到底（德邦落地配目前都是寄付月结）
        if (DpDeliveryOrderSignUtil.flag(orderModel)) {
            List<String> whiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.DP_DELIVERY_AFTER_PICK_UP_PRODUCT_WHITE_LIST,",");
            modifyWhitelist.addAll(whiteList);
        }

        // 黑名单
        Set<String> modifyBlacklist = getModifyBlacklist(orderModel);

        // 从白名单移除黑名单内容
        if (CollectionUtils.isNotEmpty(modifyWhitelist) && CollectionUtils.isNotEmpty(modifyBlacklist)) {
            modifyWhitelist.removeAll(modifyBlacklist);
        }

        List<String> illegalProductCodeList = modifyProductList.stream()
                .map(ModifyProduct::getProductNo)
                .filter(productNo -> !modifyWhitelist.contains(productNo))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(illegalProductCodeList)) {
            String illegalProductCodes = String.join(" ", illegalProductCodeList);
            LOGGER.error("揽收后，不允许修改的产品:{}", illegalProductCodes);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收后，不允许修改的产品:" + illegalProductCodes);
        }
    }

    /**
     * 寄付现结：校验增值产品能否修改（新增、删除、更新）
     */
    private void cashOnPickValidateProductModify(ExpressOrderModel orderModel, List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        // 只能新增快运改址、先到先送、修改等通知派送的产品要素、修改通电验机的产品要素
        Set<String> modifyWhitelist = new HashSet<>();
        modifyWhitelist.add(AddOnProductEnum.READDRESS_FREIGHT.getCode());
        modifyWhitelist.add(AddOnProductEnum.FIRST_COME_FIRST_SERVED.getCode());
        modifyWhitelist.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
        modifyWhitelist.add(AddOnProductEnum.FRIGHT_TEST_EQUIPMENT.getCode());
        modifyWhitelist.add(AddOnProductEnum.FRIGHT_NIGHT_COLLECTION.getCode());
        modifyWhitelist.add(AddOnProductEnum.BOOK_DELIVERY_FREIGHT.getCode());
        modifyWhitelist.add(AddOnProductEnum.FREIGHT_RECYCLE_PACKAGING.getCode());
        modifyWhitelist.add(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode());

        if (!hasCOD(orderModel)) {
            // 没有代收货款才允许修改重货上楼和送货入仓
            modifyWhitelist.add(AddOnProductEnum.HEAVY_UPSTAIR_TOB.getCode());
            modifyWhitelist.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
        }

        List<String> illegalProductCodeList = modifyProductList.stream()
                .map(ModifyProduct::getProductNo)
                .filter(productNo -> !modifyWhitelist.contains(productNo))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(illegalProductCodeList)) {
            if (hasCOD(orderModel)) {
                LOGGER.error("寄付揽收后修改不支持修改原单包含代收货款的单据，{}产品信息发生修改", illegalProductCodeList);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("寄付揽收后修改不支持修改原单包含代收货款的单据");
            } else {
                LOGGER.error("不允许修改的产品:{}", illegalProductCodeList);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("寄付现结揽收后，" + ProductDelegate.buildAddOnProductInfo(illegalProductCodeList) + "产品信息不允许修改");
            }
        }
    }

    /**
     * 月结、到付现结：获取可修改的产品编码
     */
    private Set<String> getAllowedModifiedProductCode(ExpressOrderModel orderModel) {
        // 港澳订单
        if (orderModel.isHKMO()) {
            // 仅月结可修改签单返还
            Set<String> set = new HashSet<>();
            if (isMonthlyPayment(orderModel)) {
                set.add(AddOnProductEnum.SIGN_RETURN_TOB.getCode());
                set.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
            }
            return set;
        }

        // 公共
        Set<String> set = AddOnProductEnum.getFreightAllowedModifiedProductCode();
        // 按条件增加可修改的产品
        addConditionalAllowedModifiedProductCode(orderModel, set);
        return set;
    }

    /**
     * 月结、到付现结：按条件增加可修改的产品
     */
    private void addConditionalAllowedModifiedProductCode(ExpressOrderModel orderModel, Set<String> allowProductCodeSet) {
        // 验证签收：订单状态在派送中之前才可以改
        if (isBeforeOrderDelivery(orderModel)) {
            allowProductCodeSet.add(AddOnProductEnum.VALIDATE_DELIVERY.getCode());
        }
        // 等通知派送：派送中前随便改，派送中及之后只能改产品要素
        allowProductCodeSet.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
        // 月结
        if (isMonthlyPayment(orderModel)) {
            // 拆木架
            allowProductCodeSet.add(AddOnProductEnum.FRIGHT_REMOVE_FRAME.getCode());
            // 配送签回单打印
            allowProductCodeSet.add(AddOnProductEnum.PRINT_SIGN_BACK.getCode());
            // 送货入仓
            allowProductCodeSet.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
            // 国补-激活校验：按是否有揽收模版ID、订单状态判断。特殊情况：拦截一单到底修改，删除国补
            if (allowModifyGovSubsidyMonthlyAfterPickup(orderModel)) {
                allowProductCodeSet.add(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode());
            }
        } else if (isMonthlyPaymentDelivery(orderModel)) {
            // 到付月结
            // 送货入仓
            allowProductCodeSet.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
        }
    }

    /**
     * 判断订单状态是否派送前
     */
    private boolean isBeforeOrderDelivery(ExpressOrderModel orderModel) {
        return orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getOrderStatus() != null
                && orderModel.getOrderSnapshot().getOrderStatus().isBeforeOrderDelivery();
    }

    /**
     * 揽收后修改，校验有修改的产品能否新增、删除、更新（修改产品要素等）
     */
    private void validateProductInsertOrDelete(ExpressOrderModel orderModel, List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        Set<String> insertBlacklist = new HashSet<>();
        Set<String> deleteBlacklist = new HashSet<>();
        Set<String> updateBlacklist = new HashSet<>();
        if (isCashOnPick(orderModel)) {
            // 寄付现结
            // 等通知派送：不能新增或删除（只能改产品要素）
            insertBlacklist.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
            deleteBlacklist.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());

            // 只有控制key为true时才执行，默认为false,不执行。
            // 原逻辑 重货上楼 & 送货入仓 都不能删除
            // 新逻辑 服务单不存在的情况下，重货上楼 & 送货入仓 都不能删除
            BatrixSwitch.applyByBoolean(BatrixSwitchKey.SERVICE_ORDER_DELETE_SWITCH
                    , (bTrue) -> {
                            // 重货上楼：不能删除
                            deleteBlacklist.add(AddOnProductEnum.HEAVY_UPSTAIR_TOB.getCode());
                            // 送货入仓：不能删除
                            deleteBlacklist.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
                    }
                    , (bFalse) -> {
                        if (CollectionUtils.isEmpty(orderModel.getOrderSnapshot().getRefOrderInfoDelegate().getServiceEnquiryOrderNos())) {
                            // 重货上楼：不能删除
                            deleteBlacklist.add(AddOnProductEnum.HEAVY_UPSTAIR_TOB.getCode());
                            // 送货入仓：不能删除
                            deleteBlacklist.add(AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode());
                        }
                    });

            // 通电验机：不能新增或删除（只能改产品要素）
            insertBlacklist.add(AddOnProductEnum.FRIGHT_TEST_EQUIPMENT.getCode());
            deleteBlacklist.add(AddOnProductEnum.FRIGHT_TEST_EQUIPMENT.getCode());
        } else {
            // 月结、到付现结
            if (!isBeforeOrderDelivery(orderModel)) {
                // 等通知派送：派送中及之后不能新增或删除（只能改产品要素）
                insertBlacklist.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
                deleteBlacklist.add(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
            }
        }

        insertBlacklist.add(AddOnProductEnum.FRIGHT_NIGHT_COLLECTION.getCode());
        deleteBlacklist.add(AddOnProductEnum.FRIGHT_NIGHT_COLLECTION.getCode());

        insertBlacklist.add(AddOnProductEnum.BOOK_DELIVERY_FREIGHT.getCode());
        deleteBlacklist.add(AddOnProductEnum.BOOK_DELIVERY_FREIGHT.getCode());

        insertBlacklist.add(AddOnProductEnum.FREIGHT_RECYCLE_PACKAGING.getCode());
        deleteBlacklist.add(AddOnProductEnum.FREIGHT_RECYCLE_PACKAGING.getCode());

        // 自行联系只能从有到无，不能从无到有
        insertBlacklist.add(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode());
        // 国补-激活校验：揽收后：普通情况不能删除国补；拦截一单到底修改可以删除国补
        if (!isInterceptionReaddressDeleteGovSubsidy(orderModel)) {
            deleteBlacklist.add(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode());
        }

        // 航空重货：揽收后，fr-a-0007签单返还不能新增、更新，只能删除
        if (isHKZH(orderModel)) {
            insertBlacklist.add(AddOnProductEnum.SIGN_RETURN_TOB.getCode());
            updateBlacklist.add(AddOnProductEnum.SIGN_RETURN_TOB.getCode());
        }

        if (insertBlacklist.isEmpty() && deleteBlacklist.isEmpty() && updateBlacklist.isEmpty()) {
            return;
        }

        for (ModifyProduct modifyProduct : modifyProductList) {
            if (insertBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.INSERT == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品揽收后不能新增", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品揽收后不能新增");
            }
            if (deleteBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.DELETE == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品揽收后不能删除", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品揽收后不能删除");
            }
            if (OperateTypeEnum.UPDATE == modifyProduct.getOperateType()
                    && updateBlacklist.contains(modifyProduct.getProductNo())) {
                LOGGER.error("{}增值产品揽收后不能更新（修改产品要素等）", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品揽收后不能更新（修改产品要素等）");
            }
        }
    }

    /**
     * 揽收前修改，校验有修改的产品能否新增或者删除
     */
    private void validateProductInsertOrDeleteBeforePickUp(ExpressOrderModel orderModel, List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        Set<String> insertBlacklist = new HashSet<>();
        Set<String> deleteBlacklist = new HashSet<>();

        //自行联系和夜揽产品冲突，所以支持揽收前删除和新增
        if (expressUccConfigCenter.isFrightContactDirectlyInsOrDelBAP()) {
            deleteBlacklist.add(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode());
            insertBlacklist.add(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode());
        }

        if (CollectionUtils.isEmpty(insertBlacklist) && CollectionUtils.isEmpty(deleteBlacklist)){
            return;
        }

        for (ModifyProduct modifyProduct : modifyProductList) {
            if (CollectionUtils.isNotEmpty(insertBlacklist) && insertBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.INSERT == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品不能新增", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品不能新增");
            }
            if (CollectionUtils.isNotEmpty(deleteBlacklist) && deleteBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.DELETE == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品不能删除", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品不能删除");
            }
        }
    }

    /**
     * 判断修改的产品是否有代收货款
     */
    private boolean hasCOD(List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return false;
        }
        Set<String> codCodeSet = new HashSet<>(AddOnProductEnum.getCodCode());
        for (ModifyProduct modifyProduct : modifyProductList) {
            if (codCodeSet.contains(modifyProduct.getProductNo())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据当前单判断原单或者修改产品是否有代收货款
     */
    private boolean hasCOD(ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getProductDelegate() == null
                || orderModel.getProductDelegate().isEmpty()) {
            return false;
        }
        // orderModel.getProductDelegate() 包含所有产品
        return CollectionUtils.isNotEmpty(orderModel.getProductDelegate().getCodProducts());
    }

    /**
     * 仅修改收件人联系方式（姓名、电话、手机）
     */
    private void validateOnlyModifyConsigneeContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 不允许未修改
        if (!changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化");
        }

        // 不允许修改收件人其他信息
        if (changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息");
        }
    }

    /**
     * 修改黑名单：校验仅修改收件人联系方式、修改联系方式
     */
    private void modifyBlacklistModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        String modifySceneRuleDesc = null;
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            modifySceneRuleDesc = "仅修改收件人联系方式";
        } else {
            modifySceneRuleDesc = "修改联系方式";
        }

        // 仅修改收件人联系方式，不允许修改发货信息
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            if (changedPropertyDelegate.consignorHaveChange()) {
                LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息");
            }
        }
        if (changedPropertyDelegate.cargoHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息");
        }
        if (changedPropertyDelegate.goodsHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息");
        }
        if (changedPropertyDelegate.financeHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息");
        }
        /*if (changedPropertyDelegate.shipmentHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息");
        }*/
        if (changedPropertyDelegate.promotionHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息");
        }
        if (changedPropertyDelegate.agreementInfosHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息");
        }
        if (changedPropertyDelegate.productHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息");
        }
    }

    /**
     * 揽收前修改，校验结算方式能否修改
     */
    private void validateBeforePickUpSettlementType(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel, boolean isDiscountAmountGreaterThanZero) {
        // 结算方式有修改才处理
        if (!changedPropertyDelegate.settlementTypeHaveChange()) {
            return;
        }
        if (orderModel == null
                || orderModel.getFinance() == null
                || orderModel.getFinance().getSettlementType() == null) {
            return;
        }
        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();

        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null
                || orderSnapshot.getFinance() == null
                || orderSnapshot.getFinance().getSettlementType() == null) {
            return;
        }
        SettlementTypeEnum settlementTypeSnapshot = orderSnapshot.getFinance().getSettlementType();

        // 需要校验的情况：原单寄付现结，修改成非寄付现结
        if (!(SettlementTypeEnum.CASH_ON_PICK == settlementTypeSnapshot && SettlementTypeEnum.CASH_ON_PICK != settlementType)) {
            return;
        }

        // 折后金额大于零，不允许修改结算方式
        if (isDiscountAmountGreaterThanZero) {
            LOGGER.error("寄付现结询价后不可更改结算方式");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄付现结询价后不可更改结算方式");
        }

    }

    /**
     * 判断折后金额是否大于零
     */
    private boolean isDiscountAmountGreaterThanZero(ExpressOrderModel orderModel) {
        // 先看订单折后金额
        boolean flag = Optional.ofNullable(orderModel.getOrderSnapshot())
                .map(ExpressOrderModel::getFinance)
                .map(Finance::getDiscountAmount)
                .map(Money::getAmount)
                .map(amount -> amount.compareTo(BigDecimal.ZERO) > 0)
                .orElse(false);
        if (flag) {
            return true;
        }
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.FREIGHT_MODIFY_QUERY_WAYBILL_SWITCH)) {
            LOGGER.info("快运修改校验查询运单详情切换开关:开启");
            // 再看运单折后金额
            WayBillQueryResponse wayBillQueryResponse = freightWayBillQueryFacade.queryWaybillDetail(orderModel.getOrderSnapshot());
            return Optional.ofNullable(wayBillQueryResponse)
                    .map(WayBillQueryResponse::getSumMoney)
                    .map(BigDecimal::valueOf)
                    .map(amount -> amount.compareTo(BigDecimal.ZERO) > 0)
                    .orElse(false);
        } else {
            LOGGER.info("快运修改校验查询运单详情切换开关:关闭");
            return false;
        }

    }

    /**
     * 揽收前修改，校验用户能否修改订单信息
     */
    private void validateBeforePickUpInitiatorType(ExpressOrderModel orderModel, boolean isDiscountAmountGreaterThanZero) {
        if (isDiscountAmountGreaterThanZero &&
                orderModel.getInitiatorType() != null
                && (InitiatorTypeEnum.CONSIGNOR == orderModel.getInitiatorType() || InitiatorTypeEnum.CONSIGNEE == orderModel.getInitiatorType())) {
            LOGGER.error("询价后不支持用户修改");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("询价后不支持用户修改");
        }
    }

    /**
     *
     * @param changedPropertyDelegate 修改代理
     * @param orderModel 订单
     */
    private void validateHKMO(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 订单类型
        String orderType = (null != orderModel.getOrderType()) ? orderModel.getOrderType().getCode() : null;
        // 修改策略为"仅修改报关信息"
        if (ModifySceneRuleUtil.isOnlyModifyCustoms(orderModel)) {
            // 如果修改的内容不在白名单中则不允许修改
            validateOnlyModifyFields(changedPropertyDelegate, orderModel);
            // 如果删除字段不在白名单中则不允许修改
            validateOnlyModifyClearFields(orderModel);
        } else if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderType)) {
            // 如果是逆向单, 则修改策略必填 "仅修改报关信息"
            LOGGER.error("港澳逆向单修改时修改策略必填--仅修改报关信息, orderNo: {} ", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳逆向单修改时修改策略必填--仅修改报关信息");
        }
    }

    /**
     * 校验修改字段是否在白名单中
     * @param changedPropertyDelegate
     * @param orderModel
     */
    private void validateOnlyModifyFields(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        Map<String, ChangedProperty> changedPropertyMap = changedPropertyDelegate.getChangedPropertyMap();
        if (changedPropertyMap == null) {
            return;
        }
        for (String code: changedPropertyMap.keySet()) {
            // 如果修改内容不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                if (null != changedPropertyMap.get(code)) {
                    stringBuilder.append(changedPropertyMap.get(code).getPropertyDesc());
                }
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 校验删除字段是否在白名单中
     * @param orderModel
     */
    private void validateOnlyModifyClearFields(ExpressOrderModel orderModel) {
        List<String> clearFields = orderModel.getClearFields();
        if (CollectionUtils.isEmpty(clearFields)) {
            return;
        }
        for (String code: clearFields) {
            // 如果删除字段不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                stringBuilder.append(code);
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 销售修改
     * 只有青龙车型费可以修改，并且需要校验
     */
    private void validateModifyBySalesperson(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderContext orderContext) {
        ExpressOrderModel orderModel = orderContext.getOrderModel();

        // 校验当前订单状态是否能使用销售修改的修改策略
        validateRuleModifyBySalesperson(orderModel);

        // 只允许修改财务信息中的费用明细
        if (changedPropertyDelegate.financeHaveChangeIgnoreFinanceDetail()) {
            LOGGER.info("只允许修改财务信息中的费用明细，其他数据不允许修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("只允许修改财务信息中的费用明细，其他数据不允许修改");
        }

        // 只允许修改青龙车型费
        validateOnlyModifySpecialFinanceDetail(orderModel, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 获取青龙车型费
        FinanceDetail blueDragonCarFee = freightFTLFeeFacade.getFinanceDetailByCostNo(orderModel, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 校验操作类型
        OperateTypeEnum operateType = blueDragonCarFee.getOperateType();
        if (operateType != null && OperateTypeEnum.UPDATE != operateType) {
            String msg = "青龙车型费(costNo="+ FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE+")只允许修改，不允许新增或删除";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 校验青龙车型费
        freightFTLFeeFacade.validateBlueDragonCarFee(orderContext, blueDragonCarFee);

        // 订单标识-修改车型费标识，设置为销售修改
        ModifyVehicleFeeOrderSignUtil.setModifyBySalesperson(this, orderModel);

        // 补全折前金额，折后金额，累计费用金额时青龙车型费用最新的
        List<FinanceDetail> financeDetails = orderModel.getOrderSnapshot().getFinance().getFinanceDetails();
        financeUtil.replaceComplementPreAmountAndDiscountAmount(orderModel, financeDetails, blueDragonCarFee);
    }

    /**
     * 校验当前订单状态是否能使用销售修改的修改策略
     */
    private void validateRuleModifyBySalesperson(ExpressOrderModel expressOrderModel) {
        ExpressOrderModel orderSnapshot = expressOrderModel.getOrderSnapshot();

        // 订单标识必须是创建询价单
        if (!TmsEnquiryBillOrderSignUtil.flag(orderSnapshot)) {
            LOGGER.error("{}修改财务失败：当前订单无需创建询价单，不支持操作销售修改", expressOrderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改财务失败：当前订单无需创建询价单，不支持操作销售修改");
        }

        // 询价状态必须是5-待销售确认或者3-待商家确认
        Finance finance = orderSnapshot.getFinance();
        if (finance == null
                || finance.getEnquiryStatus() == null
                || (EnquiryStatusEnum.WAITE_CONFIRM_SALE != finance.getEnquiryStatus() && EnquiryStatusEnum.WAITE_CONFIRM != finance.getEnquiryStatus())) {
            LOGGER.error("{}修改财务失败：当前询价状态不支持操作销售修改", expressOrderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改财务失败：当前询价状态不支持操作销售修改");
        }

        // 必须有整车服务费 todo getOriginalServiceFeeAmount 是兼容方法，后续需要改动
        freightFTLFeeFacade.getOriginalServiceFeeAmount(orderSnapshot);

        // 必须有青龙车型费
        freightFTLFeeFacade.getFinanceDetailByCostNo(orderSnapshot, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 销售修改后，询价状态由5-待销售确认变为3-待商家确认
        if (EnquiryStatusEnum.WAITE_CONFIRM != orderSnapshot.getFinance().getEnquiryStatus()) {
            expressOrderModel.complement().complementEnquiryStatus(this, EnquiryStatusEnum.WAITE_CONFIRM);
        }
    }

    /**
     * 新增压车费
     * 只能新增一次压车费
     */
    private void validateInsertExtrudeFee(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 校验当前订单状态是否能使用新增压车费的修改策略
        validateRuleInsertExtrudeFee(orderModel);

        // 只有月结可以改
        if (!isMonthlyPayment(orderModel) &&
                !isMonthlyPaymentDelivery(orderModel)) {
            String msg = "结算方式不是寄付月结或到付月结，不支持修改压车费(costNo="+ FreightFTLConstants.COST_NO_EXTRUDE_FEE+")";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 只允许修改财务信息中的费用明细
        if (changedPropertyDelegate.financeHaveChangeIgnoreFinanceDetail()) {
            LOGGER.info("只允许修改财务信息中的费用明细，其他数据不允许修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("只允许修改财务信息中的费用明细，其他数据不允许修改");
        }

        // 只允许修改压车费
        validateOnlyModifySpecialFinanceDetail(orderModel, FreightFTLConstants.COST_NO_EXTRUDE_FEE);

        // 只能改一次，根据原单标位判断是否修改过
        String originSign = modifyMarkHolder.getPositionMark(ModifyMarkEnum.EXTRUDE_FEE);
        if (MODIFY_MARK_YES.equals(originSign)) {
            String msg = "压车费(costNo="+ FreightFTLConstants.COST_NO_EXTRUDE_FEE+")只允许新增一次，不允许再次新增、修改或删除";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 获取压车费
        FinanceDetail extrudeFee = freightFTLFeeFacade.getFinanceDetailByCostNo(orderModel, FreightFTLConstants.COST_NO_EXTRUDE_FEE);

        // 必须有折后金额
        if (extrudeFee.getDiscountAmount() == null || extrudeFee.getDiscountAmount().getAmount() == null) {
            String msg = "压车费(costNo="+ FreightFTLConstants.COST_NO_EXTRUDE_FEE+")的折后金额(discountAmount)为空";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 折后金额必须大于零
        if (extrudeFee.getDiscountAmount().getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            String msg = "压车费(costNo="+ FreightFTLConstants.COST_NO_EXTRUDE_FEE+")的折后金额(discountAmount)必须大于零";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 校验操作类型
        OperateTypeEnum operateType = extrudeFee.getOperateType();
        if (operateType != null && OperateTypeEnum.INSERT != operateType) {
            String msg = "压车费(costNo="+ FreightFTLConstants.COST_NO_EXTRUDE_FEE+")操作类型只能是新增";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 记录更新标位
        modifyMarkHolder.addUpdateMarkRecord(ModifyMarkEnum.EXTRUDE_FEE, MODIFY_MARK_YES);

        // 补全折前金额，折后金额，累计费用金额时压车费用最新的
        List<FinanceDetail> financeDetails = orderModel.getOrderSnapshot().getFinance().getFinanceDetails();
        financeUtil.replaceComplementPreAmountAndDiscountAmount(orderModel, financeDetails, extrudeFee);
    }

    /**
     * 校验当前订单状态是否能使用新增压车费的修改策略
     */
    private void validateRuleInsertExtrudeFee(ExpressOrderModel expressOrderModel) {
        ExpressOrderModel orderSnapshot = expressOrderModel.getOrderSnapshot();
        OrderStatus orderStatus = orderSnapshot.getOrderStatus();
        // 只卡控妥投、拒收、已取消不能新增压车费
        if (orderStatus == null
                || orderStatus.getOrderStatus() == null
                || OrderStatusEnum.CUSTOMER_SIGNED == orderStatus.getOrderStatus()
                || OrderStatusEnum.CUSTOMER_REJECTED == orderStatus.getOrderStatus()
                || OrderStatusEnum.CANCELED == orderStatus.getOrderStatus()) {
            LOGGER.error("{}修改财务失败：当前订单状态不允许操作新增压车费", expressOrderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改财务失败：当前订单状态不允许操作新增压车费");
        }
    }

    /**
     * 营业厅修改
     * 只有青龙车型费可以修改，并且需要校验
     */
    private void validateModifyByBusinessHall(ModifyMarkHolder modifyMarkHolder, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderContext orderContext) {
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        // 订单标识必须是创建询价单
        if (!TmsEnquiryBillOrderSignUtil.flag(orderSnapshot)) {
            LOGGER.error("{}修改财务失败：当前订单无需创建询价单，不支持操作营业厅修改", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改财务失败：当前订单无需创建询价单，不支持操作营业厅修改");
        }

        // 询价状态必须是3-待商家确认
        Finance finance = orderSnapshot.getFinance();
        if (finance == null
                || finance.getEnquiryStatus() == null
                || EnquiryStatusEnum.WAITE_CONFIRM != finance.getEnquiryStatus()) {
            LOGGER.error("{}修改财务失败：当前询价状态不支持操作营业厅修改", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改财务失败：当前询价状态不支持操作营业厅修改");
        }

        // 必须有整车服务费
        freightFTLFeeFacade.getFinanceExtendPropsFeeAmount(orderSnapshot, FinanceConstants.ORIGINAL_SERVICE_FEE_AMOUNT, "原始整车服务费金额");

        // 快照必须有青龙车型费
        freightFTLFeeFacade.getFinanceDetailByCostNo(orderSnapshot, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 车型费只允许修改一次
        if (ModifyVehicleFeeOrderSignUtil.hasModified(orderSnapshot)) {
            String msg = "修改财务失败：已修改过车型费(costNo="+ FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE+")不允许再次修改";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 只允许修改财务信息中的费用明细
        if (changedPropertyDelegate.financeHaveChangeIgnoreFinanceDetail()) {
            LOGGER.info("只允许修改财务信息中的费用明细，其他数据不允许修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("只允许修改财务信息中的费用明细，其他数据不允许修改");
        }

        // 只允许修改青龙车型费
        validateOnlyModifySpecialFinanceDetail(orderModel, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 获取当前单青龙车型费
        FinanceDetail blueDragonCarFee = freightFTLFeeFacade.getFinanceDetailByCostNo(orderModel, FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE);

        // 校验操作类型
        OperateTypeEnum operateType = blueDragonCarFee.getOperateType();
        if (operateType != null && OperateTypeEnum.UPDATE != operateType) {
            String msg = "青龙车型费(costNo="+ FreightFTLConstants.COST_NO_BLUE_DRAGON_CAR_FEE+")只允许修改，不允许新增或删除";
            LOGGER.error(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }

        // 校验青龙车型费
        freightFTLFeeFacade.validateBlueDragonCarFee(orderContext, blueDragonCarFee);

        // 订单标识-修改车型费标识，设置为营业厅修改
        ModifyVehicleFeeOrderSignUtil.setModifyByBusinessHall(this, orderModel);

        // 补全询价状态为已确认
        orderModel.complement().complementEnquiryStatus(this, EnquiryStatusEnum.CONFIRMED);

        // 补全折前金额，折后金额，累计费用金额时青龙车型费用最新的
        List<FinanceDetail> financeDetails = orderModel.getOrderSnapshot().getFinance().getFinanceDetails();
        financeUtil.replaceComplementPreAmountAndDiscountAmount(orderModel, financeDetails, blueDragonCarFee);
    }

    /**
     * 只允许修改指定费用明细
     */
    private void validateOnlyModifySpecialFinanceDetail(ExpressOrderModel orderModel, String costNo) {
        if (orderModel.getFinance() == null
                || CollectionUtils.isEmpty(orderModel.getFinance().getFinanceDetails())) {
            return;
        }
        List<FinanceDetail> financeDetails = orderModel.getFinance().getFinanceDetails();
        for (FinanceDetail financeDetail : financeDetails) {
            if (costNo.equals(financeDetail.getCostNo())) {
                continue;
            }
            if (financeDetail.getOperateType() != null) {
                String msg = "费用明细(costNo=" + financeDetail.getCostNo() + ")不允许新增修改删除";
                LOGGER.error(msg);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
            }
        }
    }

    /**
     * 修改联系方式（姓名、电话、手机）
     */
    private void validateModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 发件人、收件人姓名电话手机不允许未修改
        if (!changedPropertyDelegate.consignorContactInformationHaveChange() &&
                !changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化");
        }

        // 不允许修改发件人、收件人其他信息
        if (changedPropertyDelegate.consignorHaveChangeIgnoreContactInformation()
                || changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息");
        }
    }

    /**
     * 仓出库发货
     */
    private void validateOutboundDelivery(ExpressOrderModel orderModel) {
        // 目前无卡控
    }

    /**
     * 特殊策略修改项校验-仅能修改货品信息
     * @param changedPropertyDelegate
     */
    private void specialModifyValid(ChangedPropertyDelegate changedPropertyDelegate){
        if (changedPropertyDelegate.getChangedProperties().size() != 1
                || !changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.CARGO)) {
            LOGGER.error("特殊修改策略specialModify仅允许需改货品");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("特殊修改策略specialModify仅允许需改货品");
        }
    }

    private void validateReaddressSystemCaller(ExpressOrderModel orderModel) {
        SystemCallerEnum snapshotSystemCaller = orderModel.getOrderSnapshot().getChannel().getSystemCaller();
        //供应链OFC来源仓配快运不支持改址一单到底
        if (SystemCallerEnum.SUPPLY_OFC == snapshotSystemCaller && SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("快运B2C订单修改白名单-供应链OFC仓配快递不支持改址一单到底");
        }
    }

    /**
     * 仅修改补签标识
     */
    private void validateModifyReSignFlag(ChangedPropertyDelegate changedPropertyDelegate) {
        // 未修改不校验
        if (changedPropertyDelegate == null
                || changedPropertyDelegate.getChangedProperties() == null
                || changedPropertyDelegate.getChangedProperties().isEmpty()) {
            return;
        }
        if (changedPropertyDelegate.getChangedProperties().size() != 1
                || !changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.RE_SIGN_FLAG)) {
            LOGGER.error("修改策略为仅修改补签标识，只能修改是否发起补签(reSignFlag)");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("修改策略为仅修改补签标识，只能修改是否发起补签(reSignFlag)");
        }
    }

    /**
     * 仅修改国补审核状态
     * 参考快递b2c，无改动
     */
    private void validateModifyGovSubsidyApprovalStatus(ExpressOrderModel orderModel, ExpressOrderModel snapshot, ChangedPropertyDelegate changedPropertyDelegate) {
        if (changedPropertyDelegate.getChangedProperties().size() != 1
                || !changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.GOV_SUBSIDY_APPROVAL_STATUS)) {
            LOGGER.error("修改策略为仅修改国补审核状态，只能修改国补审核状态");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("修改策略为仅修改国补审核状态，只能修改国补审核状态");
        }

        // 判断时间
        Date channelOperateTime = orderModel.getChannel().getChannelOperateTime();
        long opTime = channelOperateTime == null ? System.currentTimeMillis() : channelOperateTime.getTime();
        String lastOpTimeStr = snapshot.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey());
        if (StringUtils.isNotBlank(lastOpTimeStr)) {
            long lastOpTime = Long.parseLong(lastOpTimeStr);
            if (opTime <= lastOpTime) {
                LOGGER.error("修改审核状态：操作时间{}早于订单上次操作时间{}，请重新发起", opTime, lastOpTime);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                        withCustom("修改审核状态：操作时间早于订单上次操作时间，请重新发起");
            }
        }
        // 记录更新时间
        orderModel.putAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey(), String.valueOf(opTime));

        // 校验国补状态
        String govSubsidyStatus = orderModel.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_APPROVAL_STATUS.getKey());
        String snapGovSubsidyStatus= snapshot.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_APPROVAL_STATUS.getKey());

        if (GOV_SUBSIDY_INITIATE_RESHOOT_STATUS.equals(govSubsidyStatus) &&
                GOV_SUBSIDY_MERCHANT_APPROVED_STATUS.equals(snapGovSubsidyStatus)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("修改审核状态：商家审核通过不允许更新为发起补拍");
        }

        if(GOV_SUBSIDY_INITIATE_RESHOOT_STATUS.equals(snapGovSubsidyStatus) &&
                GOV_SUBSIDY_MERCHANT_APPROVED_STATUS.equals(govSubsidyStatus)){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("修改审核状态：发起补拍不允许更新为商家审核通过");
        }
    }

    /**
     * 判断寄付月结揽收后能否修改国补
     */
    private boolean allowModifyGovSubsidyMonthlyAfterPickup(ExpressOrderModel orderModel) {
        // 特殊情况：拦截一单到底修改，删除国补
        if (isInterceptionReaddressDeleteGovSubsidy(orderModel)) {
            return true;
        }

        // 派送中及之后都不允许改
        if (!isBeforeOrderDelivery(orderModel)) {
            return false;
        }

        // 派送中前，没有产品要素揽收模版ID可以改。是否有产品要素揽收模版ID：从当前单和快照判断
        boolean hasSpecialProductAttrs = hasSpecialProductAttrs(orderModel, AddOnProductEnum.ACTIVATION_CHECK_FREIGHT, AddOnProductAttrEnum.ACTIVATION_CHECK_COLLECT_TEMPLATE)
                || hasSpecialProductAttrs(orderModel.getOrderSnapshot(), AddOnProductEnum.ACTIVATION_CHECK_FREIGHT, AddOnProductAttrEnum.ACTIVATION_CHECK_COLLECT_TEMPLATE);
        return !hasSpecialProductAttrs;
    }

    /**
     * 判断是否存在指定增值产品，并且存在指定产品要素（存在key）。未考虑删除、修改等操作类型。
     */
    private boolean hasSpecialProductAttrs(ExpressOrderModel orderModel, AddOnProductEnum addOnProductEnum, AddOnProductAttrEnum addOnProductAttrEnum) {
        if (orderModel == null) {
            return false;
        }
        ProductDelegate productDelegate = orderModel.getProductDelegate();
        if (productDelegate == null || productDelegate.isEmpty()) {
            return false;
        }
        Product product = productDelegate.getByProductNo(addOnProductEnum.getCode());
        if (product== null) {
            return false;
        }
        Map<String, String> productAttrs = product.getProductAttrs();
        if (MapUtils.isEmpty(productAttrs)) {
            return false;
        }
        return productAttrs.containsKey(addOnProductAttrEnum.getCode());
    }

    /**
     * 校验揽收后改址，是否存在不允许改址的产品
     * 修改策略包括：揽收后修改（同城同站修改）、揽收后改址
     */
    private void validateReaddressProductBlacklist(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
        // fr-a-0086 存在国补，不允许揽收后改址
        if (orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode()) != null) {
            Product product = orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode());
            // 揽收后：普通情况不能删除国补；拦截一单到底修改可以删除国补
            if (isInterceptionReaddressDeleteGovSubsidy(orderModel)) {
                LOGGER.info("快运拦截一单到底修改，删除国补");
            } else {
                String msg = "存在不允许揽收后改址的增值产品：" + AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode();
                LOGGER.info(msg);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
            }
        }

        // fr-m-0007 航空重货，不允许揽收后改址
        if (isHKZH(orderModel)) {
            String msg = "存在不允许揽收后改址的产品：" + ProductEnum.HKZH.getDesc() + "("+ProductEnum.HKZH.getCode()+")";
            LOGGER.info(msg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
        }
    }

    /**
     * 是否快运拦截一单到底修改，删除国补。
     * 校验是否有改址产品，见 validateSameSiteReaddressConsignee 和 validateReaddressConsignee。
     * PRD：若改址请求包含改址服务且改址类型=拦截一单时，允许【激活校验 fr-a-0086】从有改无
     */
    private boolean isInterceptionReaddressDeleteGovSubsidy(ExpressOrderModel orderModel) {
        Product product = orderModel.getProductDelegate() != null
                ? orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode())
                : null;
        return orderModel.isInterceptionReaddressModify()
                && product != null && OperateTypeEnum.DELETE == product.getOperateType();
    }

    /**
     * 代收货款金额是否修改
     * @param orderModel
     * @return
     */
    public static boolean codAmountIsUpdate(ExpressOrderModel orderModel) {
        BigDecimal targetShouldPayMoney = BigDecimal.ZERO;//新cod金额
        BigDecimal originShouldPayMoney = BigDecimal.ZERO;//原始cod金额
        Product targetCod = orderModel.getProductDelegate().getCodProduct();
        Product originCod = orderModel.getOrderSnapshot().getProductDelegate().getCodProduct();

        if(null != targetCod
                && (targetCod.getOperateType() == null || OperateTypeEnum.DELETE != targetCod.getOperateType())
                && targetCod.getProductAttrs().containsKey(AddOnProductAttrEnum.COD.getCode())){
            targetShouldPayMoney = new BigDecimal(targetCod.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        }
        if (null != originCod && originCod.getProductAttrs().containsKey(AddOnProductAttrEnum.COD.getCode())) {
            originShouldPayMoney = new BigDecimal(originCod.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        }
        LOGGER.info("cod是否变更，originShouldPayMoney:{}, targetShouldPayMoney:{}", originShouldPayMoney, targetShouldPayMoney);
        //根据代收货款金额是否变更
        return targetShouldPayMoney.compareTo(originShouldPayMoney) != 0;
    }

    /**
     * 判断主产品是否航空重货
     *
     * @param orderModel 当前单
     */
    private boolean isHKZH(ExpressOrderModel orderModel) {
        return isSpecialMainProduct(ProductEnum.HKZH, orderModel);
    }

    /**
     * 判断修改场景是否指定的主产品
     *
     * @param mainProductEnum 指定的主产品
     * @param orderModel 当前单
     * @return
     */
    private boolean isSpecialMainProduct(ProductEnum mainProductEnum, ExpressOrderModel orderModel) {
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

        // 获取产品代理：优先从当前单取；如果修改请求没有修改产品（当前单ProductDelegate为null），从快照取
        ProductDelegate productDelegate = orderModel.getProductDelegate();
        if ((productDelegate == null || productDelegate.isEmpty())
                && (snapshot != null && snapshot.getProductDelegate() != null && !snapshot.getProductDelegate().isEmpty())) {
            productDelegate = snapshot.getProductDelegate();
        }
        if (productDelegate == null || productDelegate.isEmpty()) {
            return false;
        }

        // 存在指定产品且未删除
        Product product = productDelegate.ofProductNo(mainProductEnum.getCode());
        return product != null && OperateTypeEnum.DELETE != product.getOperateType();
    }

    /**
     * 月结、到付现结，揽收后修改，或者修改产品黑名单。
     * 后续从白名单移除黑名单内容
     *
     * @param orderModel 当前单
     */
    private Set<String> getModifyBlacklist(ExpressOrderModel orderModel) {
        Set<String> set = new HashSet<>();

        // fr-m-0007 航空重货，揽收后不允许修改保价、包装
        if (isHKZH(orderModel)) {
            // 保价
            set.addAll(AddOnProductEnum.getInsuredValueCode());
            // 包装
            set.addAll(AddOnProductEnum.getPackageServiceCode());
        }

        return set;
    }

    /**
     * 判断是否允许修改发货人地址：揽收前修改 并且 特殊SystemSubCaller
     *
     * @param modifySceneRule 修改策略
     * @param orderModel 当前单
     */
    private boolean allowModifyConsignorAddress(String modifySceneRule, ExpressOrderModel orderModel) {
        return ModifySceneRuleUtil.isBeforePickUp(modifySceneRule)
                && orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getChannel() != null
                && StringUtils.isNotBlank(orderModel.getOrderSnapshot().getChannel().getSystemSubCaller())
                && BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.ALLOW_MODIFY_CONSIGNOR_ADDRESS_SYSTEM_SUB_CALLER_LIST, ",")
                .contains(orderModel.getOrderSnapshot().getChannel().getSystemSubCaller());
    }
}