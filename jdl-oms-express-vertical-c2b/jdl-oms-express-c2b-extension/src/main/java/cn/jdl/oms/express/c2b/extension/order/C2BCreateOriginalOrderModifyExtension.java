package cn.jdl.oms.express.c2b.extension.order;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.order.IOrderExtension;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ProductClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 退货单下单成功删除原单产品
 */
@Slf4j
@Extension(code = ExpressOrderProduct.CODE)
public class C2BCreateOriginalOrderModifyExtension implements IOrderExtension {

    /**
     * UMP工具
     */
    @Resource
    private UmpUtil umpUtil;
    
    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".execute");
        try {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderSnapshot() == null) {
                return;
            }
            log.info("C2B原单修改扩展点执行开始!");
            // 参考B2C，但是C2B没有改址换单，仅处理退货单
            // 退货单后款订单
            if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()
                    && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                // 退货单后款订单，只有需要删除的增值产品不为空，才发
                log.info("C2B退货单后款订单异步清理原单增值服务执行开始!");
                List<String> clearProductNos = reverseOrReaddressDeleteOriginalProduct(orderModel);

                if (CollectionUtils.isNotEmpty(clearProductNos)) {
                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    ProductClearPdqMessageDto productClearPdqMessageDto = new ProductClearPdqMessageDto();
                    productClearPdqMessageDto.setOrderNo(orderModel.orderNo());
                    productClearPdqMessageDto.setOriginalOrderNo(orderModel.getOrderSnapshot().orderNo());
                    productClearPdqMessageDto.setRequestProfile(orderModel.requestProfile());
                    productClearPdqMessageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
                    productClearPdqMessageDto.setClearProductNos(clearProductNos);
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(productClearPdqMessageDto));
                    schedulerMessage.setDtoClass(ProductClearPdqMessageDto.class);
                    schedulerService.addSchedulerTask(PDQTopicEnum.ORIGINAL_ORDER_MODIFY, schedulerMessage, FlowConstants.EXPRESS_ORDER_ORIGINAL_ORDER_MODIFY_FLOW_CODE);
                } else {
                    log.info("C2B退货单后款订单，无需删除原单增值产品");
                }
            }
            log.info("C2B退货单后款订单异步清理原单增值服务执行结束!");
        } catch (BusinessDomainException e) {
            Profiler.functionError(callerInfo);
            log.error("C2B原单修改扩展点执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            log.error("C2B原单修改扩展点执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ORIGINAL_ORDER_MODIFY_FAIL).withCustom("C2B原单修改扩展点执行异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private List<String> reverseOrReaddressDeleteOriginalProduct(ExpressOrderModel model) {
        List<String> productNos = new ArrayList<>();

        // 新单必须是逆向单或改址单
        if (OrderTypeEnum.RETURN_ORDER != model.getOrderType()
                && OrderTypeEnum.READDRESS != model.getOrderType()) {
            return productNos;
        }

        // 原单必须是到付或者月结
        ExpressOrderModel orderSnapshot = model.getOrderSnapshot();
        SettlementTypeEnum snapshotSettlementType = orderSnapshot.getFinance().getSettlementType();
        if (SettlementTypeEnum.CASH_ON_DELIVERY != snapshotSettlementType
                && SettlementTypeEnum.MONTHLY_PAYMENT != snapshotSettlementType) {
            return productNos;
        }

        // 新单是改址单 并且 新单月结 并且 原单到付，不处理（产品反馈计费可以处理这种情况）
        SettlementTypeEnum settlementType = model.getFinance().getSettlementType();
        if (OrderTypeEnum.READDRESS == model.getOrderType()
                && SettlementTypeEnum.MONTHLY_PAYMENT == settlementType
                && SettlementTypeEnum.CASH_ON_DELIVERY == snapshotSettlementType) {
            return productNos;
        }

        // 必须是配置的增值产品
        Set<String> deleteProductNoSet = new HashSet<>(BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.REVERSE_OR_READDRESS_DELETE_ORIGINAL_PRODUCT_NO_LIST,","));
        if (!deleteProductNoSet.isEmpty()
                && orderSnapshot.getProductDelegate() != null
                && !orderSnapshot.getProductDelegate().isEmpty()) {
            List<Product> products = (List<Product>) orderSnapshot.getProductDelegate().getProducts();
            for (Product product : products) {
                if (deleteProductNoSet.contains(product.getProductNo())) {
                    productNos.add(product.getProductNo());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(productNos)) {
            log.info("需要删除的产品=" + JSONUtils.beanToJSONDefault(productNos));
        }
        return productNos;
    }
}