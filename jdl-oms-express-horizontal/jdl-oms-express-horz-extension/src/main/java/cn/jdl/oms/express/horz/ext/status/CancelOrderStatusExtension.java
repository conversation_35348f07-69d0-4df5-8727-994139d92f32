package cn.jdl.oms.express.horz.ext.status;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.status.IOrderStatusExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.AllowCancelStatusFacade;
import cn.jdl.oms.express.domain.infrs.acl.util.ChannelUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.OrderSignUtils;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.shared.common.dict.CancelInterceptTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.matrix.sdk.annotation.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description:订单业务状态校验判断扩展点
 * @create 2021-03-20 12:10
 **/
@Extension(code = ExpressOrderProduct.CODE)
public class CancelOrderStatusExtension implements IOrderStatusExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelOrderStatusExtension.class);

    @Resource
    AllowCancelStatusFacade allowCancelStatusFacade;

    /**
     * 订单可取消状态校验
     *
     * @param expressOrderContext
     * @throws AbilityExtensionException
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        //拼接订单状态校验的信息
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

        // 港人北上自提暂存单 已支付不允许取消
        if (snapshot.isSelfPickupTemporaryStorageOrder()) {
            Finance finance = snapshot.getFinance();
            if (PaymentStatusEnum.COMPLETE_PAYMENT == finance.getPaymentStatus()) {
                LOGGER.error("自提暂存服务单已支付不允许取消,订单号为 {}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.COMPLETE_PAYMENT_SERVICE_ENQUIRY_ORDER_CANCEL_FAIL).withCustom("自提暂存服务单已支付不允许取消");
            } else {
                // 允许取消设置取消成功
                orderModel.setCancelStatus(this, CancelStatusEnum.CANCEL_SUCCESS);
                return;
            }
        }

        if (!allowCancel(orderModel)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom("订单状态不允许取消");
        }

        // 如果订单是跨城急送，且订单状态下发后，当订单快照不存在中铁运单号时，不允许取消
        OrderStatus orderStatus = snapshot.getOrderStatus();
        String majorProductNo = snapshot.getProductDelegate().getMajorProductNo();
        if (ProductEnum.KCJS.getCode().equals(majorProductNo) && orderStatus.isAfterIssued()) {
            String refOrderNo = snapshot.getRefOrderInfoDelegate().getRefOrderNo(RefOrderExtendTypeEnum.ACTUAL_WAYBILL_NO);
            if (null == refOrderNo) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("实际履约运单信息尚未回传，暂不允许取消");
            }
        }
    }

    /**
     * 是否允许取消
     *
     * @param orderModel
     */
    private boolean allowCancel(ExpressOrderModel orderModel) {
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        //2.订单如果为已取消,则不允许重复取消
        if (ChannelUtil.isTaoTianSpecial(orderModel)) {
            // 若当前单为淘天，放开取消拦截状态卡控
            LOGGER.info("淘天放开取消拦截状态卡控");

        } else if (OrderStatusEnum.CANCELED == orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus()) {
            LOGGER.error("订单已经取消,重复取消失败,订单号为 {}",orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REPEAT_CANCEL).withCustom("订单已经取消,请勿重复取消");
        }
        // 如果OFC来源取消订单,则不做订单状态判断,直接返回true
        // 这个地方只有ExpressOFC会进入此分支
        if (SystemCallerEnum.EXPRESS_OFC == orderModel.getChannel().getSystemCaller()) {
            LOGGER.info("OFC来源取消订单,则不做订单状态判断,直接返回true,订单号为 {}",orderModel.orderNo());
            return true;
        }
        //3.改址单取消
        if (OrderTypeEnum.READDRESS == orderModel.getOrderSnapshot().getOrderType()) {
            Finance finance = orderModel.getOrderSnapshot().getFinance();
            //先款订单支付成功后不支持取消
            if (PaymentStageEnum.ONLINEPAYMENT == finance.getPaymentStage() && PaymentStatusEnum.COMPLETE_PAYMENT == finance.getPaymentStatus()) {
                LOGGER.error("先款订单支付成功后不支持取消,订单号为 {}",orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ONLINE_PAYMENT_CANCEL_FAIL).withCustom("先款订单支付成功后不支持取消");
            }
            //后款订单已下发不支持取消
            else if (PaymentStageEnum.CASHONDELIVERY == finance.getPaymentStage() && OrderStatusEnum.ISSUED.getCode() <= orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus().getCode()) {
                LOGGER.error("后款订单已下发不支持取消,订单号为 {}",orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CASHON_DELIVERY_CANCEL_FAIL).withCustom("后款订单已下发不支持取消");
            }
        }
        // 4. 前置仓业务允许直接取消
        if (OrderSignUtils.isFrontWarehouse(snapshot)) {
            LOGGER.info("前置仓订单，允许取消");
            return true;
        }

        if (CancelInterceptTypeEnum.ONLY_CANCEL.equals(orderModel.getCancelInterceptType())) {
            return allowCancelStatusFacade.allowCancelStatus(orderModel.requestProfile().getTenantId(), orderModel.getOrderBusinessIdentity(), orderModel.getOrderSnapshot().getCustomStatus());
        } else if (CancelInterceptTypeEnum.CANCEL_FAIL_INTERCEPT.equals(orderModel.getCancelInterceptType())) {
            return allowCancelStatusFacade.allowInterceptStatus(orderModel.requestProfile().getTenantId(), orderModel.getOrderBusinessIdentity(), orderModel.getOrderSnapshot().getCustomStatus());
        } else {
            //取消拦截类型传其他值校验不通过
            LOGGER.error("取消拦截类型传值不正确，不支持取消。订单号：{}，取消拦截类型：{}",orderModel.orderNo(),orderModel.getCancelInterceptType().getCode());
            return false;
        }
    }



}
