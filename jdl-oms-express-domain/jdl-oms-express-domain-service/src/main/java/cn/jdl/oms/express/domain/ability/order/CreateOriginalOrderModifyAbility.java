package cn.jdl.oms.express.domain.ability.order;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.order.IOrderExtension;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @Package： cn.jdl.oms.express.domain.ability.product
 * @ClassName: CreateProductAbility
 * @Description: 接单原单修改能力
 * @Author： wangjingzhao
 * @CreateDate 2021/3/19 4:41 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@DomainAbility(name = "纯配领域能力-接单原单修改能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.CREATE, isDefault = false)
public class CreateOriginalOrderModifyAbility extends AbstractDomainAbility<ExpressOrderContext, IOrderExtension> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CreateOriginalOrderModifyAbility.class);

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 纯配领域能力-接单原单修改能力
     * <AUTHOR>
     * @createDate 2021/3/19 4:44 下午
     * @lastModify
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) throws DomainAbilityException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            // 改址单后款订单 或者 B2C/C2B的退货单单后款订单
            if (readdressCashOnDelivery(expressOrderContext)
                    || (b2cOrC2b(expressOrderContext) && (returnOrderCashOnDelivery(expressOrderContext)))) {
                IOrderExtension extension = this.getMiddleExtensionFast(IOrderExtension.class,
                        expressOrderContext,
                        SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
                LOGGER.info("纯配领域能力-接单原单修改能力活动执行开始");
                if (extension != null) {
                    extension.execute(expressOrderContext);
                } else {
                    LOGGER.info("扩展点为空");
                }
                LOGGER.info("纯配领域能力-接单原单修改能力活动执行结束");
            }
        } catch (AbilityExtensionException e) {
            LOGGER.info("纯配领域能力-接单原单修改能力执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.info("纯配领域能力-接单产品校验能力执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.ORIGINAL_ORDER_MODIFY_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @Override
    public IOrderExtension getDefaultExtension() {
        return null;
    }

    /**
     * 改址单后款订单
     */
    private boolean readdressCashOnDelivery(ExpressOrderContext expressOrderContext) {
        return OrderTypeEnum.READDRESS == expressOrderContext.getOrderModel().getOrderType()
                && PaymentStageEnum.CASHONDELIVERY == expressOrderContext.getOrderModel().getFinance().getPaymentStage();
    }

    /**
     * 退货单单后款订单
     */
    private boolean returnOrderCashOnDelivery(ExpressOrderContext expressOrderContext) {
        return OrderTypeEnum.RETURN_ORDER == expressOrderContext.getOrderModel().getOrderType()
                && PaymentStageEnum.CASHONDELIVERY == expressOrderContext.getOrderModel().getFinance().getPaymentStage();
    }

    /**
     * B2C或者C2B
     */
    private boolean b2cOrC2b(ExpressOrderContext expressOrderContext) {
        return expressOrderContext.getOrderModel().isB2C() || expressOrderContext.getOrderModel().isC2B();
    }
}

