package cn.jdl.oms.express.domain.ability.serviceEnquiry;


import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ChannelMapper;
import cn.jdl.oms.express.domain.extension.serviceOrder.IAsyncCancelServiceOrderExtension;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CancelOrderMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.CancelTypeEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 纯配领域能力-服务单异步取消能力
 * @Creator: liujiangwai1
 * @Date: 2025/1/8
 */
@Slf4j
@DomainAbility(name = "纯配领域能力-服务单异步取消能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.MODIFY, isDefault = false)
public class AsyncCancelServiceOrderAbility extends AbstractDomainAbility<ExpressOrderContext, IAsyncCancelServiceOrderExtension> {

    @Resource
    private UmpUtil umpUtil;

    /** 纯配订单jmq服务生产者 */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Override
    public void execute(ExpressOrderContext context, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".execute");
        try {
            ExpressOrderModel orderModel = context.getOrderModel();
            ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
            if (CollectionUtils.isEmpty(snapshot.getRefOrderInfoDelegate().getServiceEnquiryOrderNos())) {
                log.info("不存在服务询价单，不执行取消服务单能力");
                return;
            }

            // 判断是否需要取消服务询价单
            if (!orderModel.needCancelServiceOrder()) {
                return;
            }

            // 发送MQ取消订单
            // 构建JMQ
            CancelOrderMessageDto cancelOrderMessageDto = new CancelOrderMessageDto();
            cancelOrderMessageDto.setRequestProfile(orderModel.requestProfile());
            cancelOrderMessageDto.setOrderNo(snapshot.getRefOrderInfoDelegate().getServiceEnquiryOrderNos().get(0));
            cancelOrderMessageDto.setCustomOrderNo(snapshot.getRefOrderInfoDelegate().getServiceEnquiryWaybillNos().get(0));
            cancelOrderMessageDto.setChannelInfo(ChannelMapper.INSTANCE.toChannelInfo(orderModel.getChannel()));
//            cancelOrderMessageDto.setCancelType(CancelTypeEnum.CT_1006.getCode());
            cancelOrderMessageDto.setOperator(orderModel.getOperator());
            expressOrderFlowService.sendCancelServiceOrderMq(cancelOrderMessageDto);

            log.info("服务单取消执行完成");

        } catch (AbilityExtensionException e) {
            log.error("服务单异步取消能力执行异常: ", e);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            log.error("服务单异步取消能力执行异常: ", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @Override
    public IAsyncCancelServiceOrderExtension getDefaultExtension() {
        return null;
    }
}
