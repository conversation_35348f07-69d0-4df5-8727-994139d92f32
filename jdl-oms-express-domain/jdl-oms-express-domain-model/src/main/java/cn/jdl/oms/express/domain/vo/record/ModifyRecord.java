package cn.jdl.oms.express.domain.vo.record;

import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 订单操作记录
 * 改址一单到底引入
 */
@Data
public class ModifyRecord implements Serializable {
    private static final long serialVersionUID = 6225399421517236788L;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 记录号
     */
    private String modifyRecordNo;

    /**
     * 记录类型
     */
    private String modifyRecordType;

    /**
     * 记录序号
     */
    private Integer modifyRecordSequence;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 记录状态
     * 0-未生效
     * 1-生效
     */
    private Integer modifyRecordStatus;

    /**
     * 记录明细
     */
    private String modifyRecordMsg;

    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    /**
     * 记录明细对象
     */
    private Object modifyRecordDetail;

    public Object getModifyRecordDetail() {
        if (null == modifyRecordDetail) {
            if (ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecordType)// 改址记录
                    || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecordType)// 拦截记录
                    || ModifyRecordTypeEnum.RECHECK.getCode().equals(modifyRecordType)// 复重量方记录
                    || ModifyRecordTypeEnum.REJECT.getCode().equals(modifyRecordType)// 拒收改址记录
            ) {
                modifyRecordDetail = JSONUtils.jsonToBean(modifyRecordMsg, ReaddressRecordDetailInfo.class);
            }
        }

        return modifyRecordDetail;
    }
}
