package cn.jdl.oms.express.domain.ability.orderbank;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.orderbank.IOrderBankExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank.C2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 清台账能力点
 */
@DomainAbility(name = "纯配领域能力-清台账生成活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = {BusinessSceneEnum.CREATE, BusinessSceneEnum.CANCEL}, isDefault = false)
public class ClearHistoryOrderBankAbility extends AbstractDomainAbility<ExpressOrderContext, IOrderBankExtension> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClearHistoryOrderBankAbility.class);

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 台账数据转换器
     */
    @Resource
    private C2COrderBankFacadeTranslator c2cOrderBankFacadeTranslator;

    /**
     * 查询实收
     */
    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("纯配领域能力-清台账活动执行开始");
            IOrderBankExtension orderBankExtension = this.getMiddleExtensionFast(IOrderBankExtension.class, expressOrderContext,
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            if (orderBankExtension != null) {
                orderBankExtension.execute(expressOrderContext);
            } else {
                ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
                ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
                if (orderSnapshot == null) {
                    LOGGER.info("不用清台账");
                    return;
                }

                if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderSnapshot.getOrderType()
                        && BusinessSceneEnum.CANCEL.getCode().equals(orderModel.getBusinessScene())) {
                    LOGGER.info("服务询价单取消清台账");
                } else if (!orderModel.isC2C() && !orderModel.isB2C() && !orderModel.isCCB2C() && !orderModel.isIntlC2C() && !orderModel.isFreight()) {
                    LOGGER.info("非C2C和B2C业务，不做台账处理操作");
                    return;
                }

                SchedulerMessage schedulerMessage = new SchedulerMessage();
                OrderBankClearPdqMessageDto orderBankClearPdqMessage = new OrderBankClearPdqMessageDto();
                orderBankClearPdqMessage.setTriggerClassName(this.getClass().getSimpleName());
                OrderBankFacadeRequest orderBankFacadeRequest = c2cOrderBankFacadeTranslator.toClearCommonOrderBankFacadeRequest(expressOrderContext, true);

                if (orderSnapshot.isSelfPickupTemporaryStorageOrder()) {
                    // 自提暂存单清B商家暂存费和POS
                    LOGGER.info("自提暂存单清台账");
                    orderBankClearPdqMessage.setClearPosYun(true);
                    orderBankClearPdqMessage.setClearBMerchantZCF(true);
                }
                else if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderSnapshot.getOrderType()) {
                    LOGGER.info("服务询价单清台账");
                    // 服务询价单用customOrderNo 运单号没有值
                    orderBankFacadeRequest.setWaybillNo(orderSnapshot.getCustomOrderNo());
                    if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(orderSnapshot.getFinance().getSettlementType())) {
                        // 到付 POS到付和B商家台账将应收清0；
                        orderBankClearPdqMessage.setClearPosYun(true);
                        orderBankClearPdqMessage.setClearBMerchantDf(true);
                    } else if (SettlementTypeEnum.CASH_ON_PICK.equals(orderSnapshot.getFinance().getSettlementType())) {
                        if (PaymentStageEnum.CASHONDELIVERY.equals(orderSnapshot.getFinance().getPaymentStage())) {
                            // 寄付后款 POS到付和B商家台账将应收清0；
                            orderBankClearPdqMessage.setClearPosJfYun(true);
                            orderBankClearPdqMessage.setClearBMerchantJf(true);
                        } else {
                            // 寄付先款 外单台账清0；
                            orderBankClearPdqMessage.setClearOts(true);
                        }
                    }
                }
                //取消场景清所有台账，取消成功
                else if (BusinessSceneEnum.CANCEL.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())
                        && CancelStatusEnum.CANCEL_SUCCESS.equals(orderModel.getCancelStatus())) {
                    if (orderModel.isFreight() || orderModel.isB2C()) {
                        if (CollectionUtils.isNotEmpty(orderSnapshot.getProductDelegate().getCodProducts())
                                && (SettlementTypeEnum.CASH_ON_DELIVERY.equals(orderSnapshot.getFinance().getSettlementType())
                                || SettlementTypeEnum.MONTHLY_PAYMENT.equals(orderSnapshot.getFinance().getSettlementType()))) {
                            orderBankClearPdqMessage.setClearPosYun(true);
                            orderBankClearPdqMessage.setClearBMerchantCod(true);
                            LOGGER.info("取消场景清账");
                        }else {
                            return;
                        }
                    }else {
                        orderBankClearPdqMessage.setClearPosYun(true);
                        orderBankClearPdqMessage.setClearPosJfYun(true);
                        orderBankClearPdqMessage.setClearBMerchantCod(true);
                        orderBankClearPdqMessage.setClearBMerchantDf(true);
                        orderBankClearPdqMessage.setClearBMerchantJf(true);
                        LOGGER.info("取消场景清账");
                    }
                }
                //改址单后款订单
                else if (OrderTypeEnum.READDRESS == orderModel.getOrderType() && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                    Map<String, String> orderNewFinanceExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
                    // 修改原单的结算方式
                    String originSettlementType = MapUtils.isNotEmpty(orderNewFinanceExt) ? orderNewFinanceExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
                    // 修改原单的结算方式
                    Integer originSettlementTypeIntVal = StringUtils.isNotBlank(originSettlementType) ? Integer.parseInt(originSettlementType) : null;
                    if (SettlementTypeEnum.CASH_ON_DELIVERY == orderSnapshot.getFinance().getSettlementType()
                            && null != originSettlementTypeIntVal
                            && SettlementTypeEnum.MONTHLY_PAYMENT == SettlementTypeEnum.of(originSettlementTypeIntVal)) {
                        orderBankClearPdqMessage.setClearPosYun(true);
                        orderBankClearPdqMessage.setClearBMerchantDf(true);
                        orderBankClearPdqMessage.setClearBMerchantCod(true);
                        LOGGER.info("改址单后款订单原单到付现结算改为寄付月结，清原单B商家&POS到付台账");
                    }
                    //若原单有COD当改址单下发成功后，清原单的POS到付（只写了cod，所以直接清没问题）和B商家台账的COD
                    //fixme 到付询价调用百川订单中心，POS台账除了COD 也写了运费,以前到付不允许改址，现在改址放开 ********
                    else if (CollectionUtils.isNotEmpty(orderSnapshot.getProductDelegate().getCodProducts())
                            && CollectionUtils.isNotEmpty(orderModel.getProductDelegate().getCodProducts())) {
                        // 清 原单POS到付
                        orderBankClearPdqMessage.setClearPosYun(true);
                        orderBankFacadeRequest.setPosYun(c2cOrderBankFacadeTranslator.generateYFPosYun(orderSnapshot, orderSnapshot.getFinance().getSettlementType()));

                        // B商家COD -- 外单台账有实收且原单后款，不清，其余清
                        String waybillNo = orderSnapshot.getRefOrderInfoDelegate().getWaybillNo();
                        if (PaymentStageEnum.CASHONDELIVERY.equals(orderSnapshot.getFinance().getPaymentStage())) {
                            if (orderModel.isC2C()
                                    && !(retailOrderBankFacade.isRetailOrderBankC2C(waybillNo, MerchantUtils.getMerchantId(orderSnapshot)))
                            ) {
                                orderBankClearPdqMessage.setClearBMerchantCod(true);
                            }
                            if (orderModel.isB2C()
                                    && !(retailOrderBankFacade.isRetailOrderBankB2C(waybillNo, MerchantUtils.getB2CMerchantId(orderSnapshot)))
                            ) {
                                orderBankClearPdqMessage.setClearBMerchantCod(true);
                            }
                        }
                        LOGGER.info("改址单后款订单原单有COD，清原单的POS到付(只写了cod，所以直接清)和B商家台账的COD");
                    } else {
                        LOGGER.info("改址单后款订单原单无COD，不用清原单台账");
                    }

                } else {
                    LOGGER.info("不支持的清理台账操作");
                    return;
                }
                orderBankClearPdqMessage.setOrderBankFacadeRequest(orderBankFacadeRequest);
                orderBankClearPdqMessage.setRequestProfile(orderModel.requestProfile());
                orderBankClearPdqMessage.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
                orderBankClearPdqMessage.setOrderNo(orderSnapshot.orderNo());
                orderBankClearPdqMessage.setCustomOrderNo(orderModel.getCustomOrderNo());

                schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(orderBankClearPdqMessage));
                schedulerMessage.setDtoClass(OrderBankClearPdqMessageDto.class);
                schedulerService.addSchedulerTask(PDQTopicEnum.CLEAR_ORDER_BANK, schedulerMessage, FlowConstants.EXPRESS_ORDER_BANK_FLOW_CODE);
            }
            LOGGER.info("纯配领域能力-清台账活动执行结束");
        } catch (AbilityExtensionException e) {
            LOGGER.info("纯配领域能力-清台账执行异常: ", e);
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.info("纯配领域能力-清台账执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.LEDGER_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    @Override
    public IOrderBankExtension getDefaultExtension() {
        return null;
    }
}
