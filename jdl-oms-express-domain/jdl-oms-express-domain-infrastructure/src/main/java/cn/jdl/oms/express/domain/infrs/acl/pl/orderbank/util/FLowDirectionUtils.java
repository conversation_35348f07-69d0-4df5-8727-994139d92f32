package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util;

import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.vo.Customs;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import static cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum.CN;
import static cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum.HK;
import static cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum.MO;

public class FLowDirectionUtils {
    /**
     * 注册器，根据始发目的流向判断流向类型
     */
    private static final Table<AdministrativeRegionEnum, AdministrativeRegionEnum, FLowDirection> REGISTRY = HashBasedTable.create();

    public enum FLowDirection {
        /** 内地到港澳 */
        CN2HKMO,
        /** 港澳到内地 */
        HKMO2CN,
        /** 港澳互寄 */
        HK2MO,
        /** 港澳同城 */
        HKMO_INTRA_CITY,
        ;
    }

    static {
        REGISTRY.put(CN, HK, FLowDirection.CN2HKMO);
        REGISTRY.put(CN, MO, FLowDirection.CN2HKMO);
        REGISTRY.put(MO, CN, FLowDirection.HKMO2CN);
        REGISTRY.put(HK, CN, FLowDirection.HKMO2CN);
        REGISTRY.put(HK, HK, FLowDirection.HKMO_INTRA_CITY);
        REGISTRY.put(MO, MO, FLowDirection.HKMO_INTRA_CITY);
        REGISTRY.put(HK, MO, FLowDirection.HK2MO);
        REGISTRY.put(MO, HK, FLowDirection.HK2MO);
    }

    public static FLowDirection getFLowDirection(Customs customs) {
        if(null == customs){
            return null;
        }
        return REGISTRY.get(customs.getStartFlowDirection(), customs.getEndFlowDirection());
    }

    public static FLowDirection getFLowDirection(AdministrativeRegionEnum start, AdministrativeRegionEnum end) {
        return REGISTRY.get(start, end);
    }

}
