#\u654F\u611F\u8BCD\u6821\u9A8C\u5F00\u5173\uFF0Ctrue\uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u5173\u95ED
sensitiveWords.switch=true
#\u9632\u5E76\u53D1\u80FD\u529B\u5F00\u5173\uFF0Ctrue\uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u5173\u95ED
antiConcurrent.switch=true
#JMQ\u53D1\u9001\u5F00\u5173
sendMessage.switch=true
#\u9006\u5411\u5355\u539F\u5355\u53F0\u8D26\u662F\u5426\u5408\u5E76\u5230\u65B0\u5355\u4E0A\u5F00\u5173
orderBankMerge.switch=true
#\u652F\u4ED8\u673A\u6784\u67E5\u8BE2\u7F13\u5B58\u5F00\u5173
addressOrgCache.switch=true
#\u53F0\u8D26\u4FEE\u6539\u56DE\u6EDA\u67E5\u8BE2\u5916\u5355\u5F00\u5173
orderBankModifyRollbackQueryLdop.switch=true
#\u53F0\u8D26\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173
orderBankSyncInit.switch=true
#B2C\u53F0\u8D26\u662F\u5426\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173\uFF0Ctrue:\u540C\u6B65,false:\u5F02\u6B65
b2cOrderBankSyncInit.switch=true
#CCB2C\u53F0\u8D26\u662F\u5426\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173\uFF0Ctrue:\u540C\u6B65,false:\u5F02\u6B65
ccB2cOrderBankSyncInit.switch=true
#CCB2B\u53F0\u8D26\u662F\u5426\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173\uFF0Ctrue:\u540C\u6B65,false:\u5F02\u6B65
ccB2BOrderBankSyncInit.switch=true
#C2B\u53F0\u8D26\u662F\u5426\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173\uFF0Ctrue:\u540C\u6B65,false:\u5F02\u6B65
c2bOrderBankSyncInit.switch=true
#\u9006\u5411\u5355-\u7ED3\u7B97\u65B9\u5F0F\u6708\u7ED3-\u662F\u5426\u8D70\u5546\u5BB6\u914D\u7F6E\u6821\u9A8C\uFF1Afalse \u4E0D\u6821\u9A8C\uFF0Ctrue\u6821\u9A8C
reverse.monthSettlement.customerConfig.switch=false
#Gis\u89E3\u6790\u5931\u8D25\u662F\u5426\u963B\u585E\u6D41\u7A0B\uFF1Atrue \u963B\u585E\uFF0Cfalse \u4E0D\u963B\u585E
gis.analysis.fail.block.flow.switch=false
#JMQ\u63A5\u5355\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.create.express.order.flow.switch=true
#JMQ\u4FEE\u6539\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.modify.express.order.flow.switch=true
#\u4FEE\u6539\u8BA2\u5355\u8D22\u52A1\u8BB0\u5F55\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.modifyFinance.express.order.flow.switch =true
#\u8BA2\u5355\u652F\u4ED8\u8BB0\u5F55\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.pay.express.order.flow.switch =true
#JMQ\u5220\u5355\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.delete.express.order.flow.switch=true
#JMQ\u53D6\u6D88\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.cancel.express.order.flow.switch=true
#JMQ\u56DE\u4F20\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.callback.express.order.flow.switch=false

#\u56DE\u4F20-\u672A\u6620\u5C04\u5230\u8BA2\u5355\u72B6\u6001\u5230\u6269\u5C55\u72B6\u6001\u767D\u540D\u5355\u914D\u7F6E
callBack.extend.status.valid.white=-1450

#\u4FEE\u6539\u9700\u8981\u6821\u9A8C\u8BE2\u4EF7\u72B6\u6001\u7684systemCaller
modify.valid.enquiry.systemCaller=WebSite,WeChat-MiniProgram,WeChat-OfficialAccount,JD-H5,Baidu-MiniProgram,JD-MiniProgram

#\u56DE\u4F20-\u9700\u8981\u5904\u7406\u7684\u6269\u5C55\u72B6\u6001\u767D\u540D\u5355\u914D\u7F6E
callBack.extend.status.handle.white=-9990,-9990,-1850,-1710,-1700,-1690,-1640,-1530,-1490,-1480,-1470,-1450,-1440,-1360,-1330,-1320,-1310,-1300,-1290,-1270,-1260,-1250,-1230,-1220,-1120,-1110,-970,-890,-875,-870,-790,-690,-650,-640,-625,-280,-275,-240,-190,10,12,13,15,16,60,80,110,130,133,135,150,160,180,200,250,500,520,530,540,570,580,590,600,620,625,630,635,700,-3020,-3030,-3040,750,-860,-100000
#\u767E\u5DDD\u76D1\u63A7\u6570\u636E\u4E0A\u62A5\u91C7\u96C6\u5F00\u5173
express.order.monitor.switch=true

# \u9006\u5411\u5355\u8BE2\u4EF7\u8BA1\u7B97\u539F\u5355\u8D39\u7528\u7F16\u7801\u914D\u7F6E
reverse.origin.order.finance.config=QIPSF,QLBJ,QLJZD,QLDZQD,C2CBJF,SXHDYF,SXSDYF,SXHDBJ,SXSDBJ,QLHCF,KDGFQFJF,TEANFW
# \u9006\u5411\u5355\u8BE2\u4EF7\u8BA1\u7B97\u539F\u5355\u8D39\u7528\u6298\u6263\u7F16\u7801\u914D\u7F6E
reverse.origin.order.discount.config=1001,1002,2001,2002,3001,3002,6001,6002,11003,8,1003,22003

#\u4FEE\u6539\u8BA2\u5355\u6570\u636E\u540C\u6B65\u4FEE\u6539\u6765\u6E90\u9ED1\u540D\u5355
modify.data.sync.source.black = 1,12
#\u63A5\u8D27\u9000\u8D27\u573A\u666F\u8FD0\u5355\u72B6\u6001\u914D\u7F6E
pick.return.waybill.status = 530,-1470,-1480,-650
#\uFFFD\uFFFD\u03F2\uFFFD\uFFFD\u0328\uFFFD\uFFFD\u036C\uFFFD\uFFFD\uFFFD\uFFFD\u02BC\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
jxdOrderBankSyncInit.switch=true
#\u53F0C2C\u521D\u59CB\u5316\u53F0\u8D26\u662F\u5426\u4F7F\u7528pdq,ture:\u4F7F\u7528pdq\uFF0Cfalse:\u4F7F\u7528jmq
c2cOrderBankSyncInitPdqSwitch=false
#\u540C\u6B65\u65B0\u589E\u589E\u503C\u670D\u52A1\u767D\u540D\u5355\u914D\u7F6E
syncNewAddOnProductWhite=ed-a-0047,ed-a-0037,ed-a-0038,ed-a-0064,ed-a-0038,ed-a-0052,ed-a-0045,ed-a-0053,ed-a-0072,ed-a-0026,ed-a-0011,ed-a-0073,ed-a-0057,ed-a-0075,ed-a-0010,fr-a-0007,LL-ZZ-QDFH,ed-a-0079,ed-a-0085,fr-a-0072,ed-a-0090,fr-a-0076,fr-a-0066,fr-a-0077,ed-a-0097,fr-a-0082,ed-a-0109
#\u540C\u6B65\u65B0\u589E\u4E3B\u4EA7\u54C1\u6269\u5C55\u5173\u8054\u4EA7\u54C1\u767D\u540D\u5355\u914D\u7F6E
syncMainProductExtRefWhite=cl-m-0002,sc-m-0036,ll-m-0014
#b2c\u5F02\u6B65\u521D\u59CB\u5316\u53F0\u8D26\u65B9\u5F0F\uFF0C\u662F\u5426\u4F7F\u7528PDQ,true:pdq,false:jmq
b2cOrderBankSyncInitPdqSwitch=true
#c2b\u5F02\u6B65\u521D\u59CB\u5316\u53F0\u8D26\u65B9\u5F0F\uFF0C\u662F\u5426\u4F7F\u7528PDQ,true:pdq,false:jmq
c2bOrderBankSyncInitPdqSwitch=false
#\u5426\u8BE2\u4EF7\u67E5\u5916\u5355\u8BE6\u60C5\u5F00\u5173
c2cEnquiryQueryWaybill.switch=false
#C2C\u521D\u59CB\u5316\u53F0\u8D26\u964D\u7EA7\u5F00\u5173
c2cInitOrderBankLower.switch=false
#\u8C03\u7528\u5730\u5740\u56F4\u680F\u4E1A\u52A1\u7EBF\u5217\u8868
invoke.address.fence.businessUnit.config=cn_jdl_ecp-pingan,cn_jdl_ecp-vipshop,cn_jdl_b2c-kuaishou
#C2C\u63FD\u6536\u540E\u4E0D\u5141\u8BB8\u4FEE\u6539\u7ED3\u7B97\u65B9\u5F0F\u5F00\u5173
c2c.notAllow.modifySettlement.afterPickedUp.switch=true

#\u6570\u79D1\u5185\u5BB9\u5B89\u5168\u6821\u9A8C\u670D\u52A1\u65B0\u5F00\u5173\uFF1Atrue\u5F00\u542F\uFF0Cfalse\u5173\u95ED
ai.sensitiveWords.switch=true
#\u89C4\u5219\u5F15\u64CE\u5F00\u5173\u63A7\u5236\uFF0Ctrue:\u5F00\u542F\uFF0Cfalse:\u5173\u95ED
drools.rule.switch=false
#\u8BA2\u5355\u6570\u636E\u6D41\u6C34\u5B57\u6BB5
dataFlowRequiredFields=PLAN_DELIVERY_TIME,EXPECT_PICKUP_START_TIME,EXPECT_PICKUP_END_TIME,ACTUAL_RECEIVED_QUANTITY,RECEIVE_PREFERENCE,CONSIGNEE_PROVINCE_NO_GIS,CONSIGNEE_CITY_NO_GIS,CONSIGNEE_COUNTY_NO_GIS,CONSIGNOR_PROVINCE_NO_GIS,CONSIGNOR_CITY_NO_GIS,CONSIGNOR_COUNTY_NO_GIS,MAIN_PRODUCT,CONSIGNEE_TOWN_NO_GIS,CONSIGNOR_TOWN_NO_GIS,CONSIGNEE_NAME,CONSIGNEE_MOBILE,CONSIGNEE_PHONE,CONSIGNEE_ADDRESS,CONSIGNOR_NAME,CONSIGNOR_MOBILE,CONSIGNOR_PHONE,CONSIGNOR_ADDRESS,CARGO,PRODUCT_NO,SETTLEMENT_TYPE,TICKET_NO,ACTIVITY_NO,DISCOUNT_NO
#\u8BA2\u5355\u6570\u636E\u6D41\u6C34\u5F00\u5173\uFF0Ctrue:\u5F00\u542F\uFF1Bfalse:\u5173\u95ED
dataFlowSwitch=true
#\u91D1\u989D\u5C0F\u6570\u4F4D\u964D\u7EA7\u62102\u4F4D\u5F00\u5173\uFF1Atrue \u5F00\u542F false \u5173\u95ED
amountScaleDownSwitch=true
#\u6E29\u5C42\u65F6\u6548\u5F00\u5173
warmLayerAgingSwitch=true

#\u539F\u5355\u672A\u652F\u4ED8\uFF0C\u751F\u6210\u9006\u5411\u5355\u65F6\u9700\u8981\u5408\u5E76\u652F\u4ED8\u7684\u652F\u4ED8\u65B9\u5F0F\u540D\u5355\u914D\u7F6E\uFF0C\u591A\u4E2A,\u5206\u5272\u3002\u914D\u7F6E\u7684\u662F\u539F\u5355\u7684\u652F\u4ED8\u65B9\u5F0F
needSumOriginalOrderPaymentWhite = 1

#\u540C\u6B65\u8FD0\u5355\u6570\u636E\u7279\u6B8A\u5904\u7406\u589E\u503C\u670D\u52A1\u7F16\u7801
sync.waybill.data.special.handler.added.product=ed-a-0010,ed-a-0022,fr-a-0055,fr-a-0063
#B2C\u57FA\u672C\u4FE1\u606F\u6821\u9A8C\u6E20\u9053\u8D27\u54C1\u6570\u91CF\u652F\u63010\u4E2A\u767D\u540D\u5355
b2c.cargo.quantity.systemCalls=EDI,JOS,CLPS,SupplyOFC
b2c.cargo.weight.systemCalls=CLPS,JOS,SupplyOFC
b2c.cargo.volume.systemCalls=SupplyOFC,CLPS
b2c.sensitive.word.SystemCallers=POP,AllInPlate
b2c.modifyCodSwitch=true
b2c.modifyWhiteSystemCallers=
b2c.jdOrderBlackList=

#\u9700\u8981\u521B\u5EFA\u5173\u8054\u5173\u7CFB\u7684\u6E20\u9053\u8C03\u7528\u65B9\u5B50\u6765\u6E90
needCreateRelationSystemSubCaller=cn_jdl_ecp-bytedance
#\u9700\u8981\u521B\u5EFA\u5173\u8054\u5173\u7CFB\u7684\u8BA2\u5355\u6807\u8BC6\u7C7B\u578B
needCreateRelationOrderSign=lordGiftType
#\u6539\u5740\u5355\u8BE2\u4EF7\u589E\u503C\u4EA7\u54C1\u767D\u540D\u5355\u914D\u7F6E
readdressEnquiryAddOnProductWhite=ed-a-0031
#\u5199\u5173\u8054\u5173\u7CFB\u65F6\u7684\u81EA\u5B9A\u4E49\u5355\u53F7\u6700\u5C0F\u957F\u5EA6
orderRelationCustomerMinLength=-1

#Batrix\u5F52\u56E0\u5E73\u53F0\u8BB0\u5F55\u603B\u5F00\u5173
batrixTracerSwitch=false
#Batrix\u5F52\u56E0\u5E73\u53F0\u9700\u8981\u8BB0\u5F55\u7684\u4E1A\u52A1\u573A\u666F\u5B57\u7B26\u4E32
batrixTracerBusinessScenes=receive,modify,cancel,query,callback,delete,init,enquiry,intercept,recover,pay
#c2c\u767E\u5DDD\u5E76\u884C\u5904\u7406\u5F00\u5173:100 100%\u5F00\u542F,0~100 \u90E8\u5206\u5F00\u542F,0\u5173\u95ED
c2cBatrixParallelProcessSwitch=0
#\u53F0\u8D26\u8F6C\u6362\u7701\u7F16\u7801\u5F00\u5173\uFF1Afalse\uFF1A\u5173\u95ED\uFF0Ctrue\uFF1A\u5F00\u542F
getGBDistrictByJDCodeSwitch = true
##C2B\u8425\u4E1A\u90E8\u8D44\u6E90\u6821\u9A8C\u53CA\u9884\u5360
c2bDepartmentResourceCheckPreemptSystemSubCaller=cn_jdl_ecp-bytedance
#\u8BB8\u4F18\u60E0\u5238\u548C\u6298\u6263\u540C\u65F6\u4F7F\u7528\u767D\u540D\u5355\uFF0C\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
allowTicketDiscountWhite=newgraduationsend
#\u5FEB\u8FD0\u9006\u5411\u539F\u5355\u8BE2\u4EF7\u589E\u503C\u4EA7\u54C1\u767D\u540D\u5355\uFF0C\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
freightReverseOriginOrderEnquiryAddOnProductWhite=fr-a-0002,fr-a-0016,fr-a-0005
#\u5FEB\u8FD0\u8D27\u7269\u91CD\u91CF\u6700\u5C0F\u503C
freightCargoWeightMin=0
#\u5FEB\u8FD0\u8D27\u7269\u91CD\u91CF\u6700\u5927\u503C
freightCargoWeightMax=9999000
#\u5FEB\u8FD0\u8D27\u7269\u4F53\u79EF\u6700\u5C0F\u503C
freightCargoVolumeMin=0
#\u5FEB\u8FD0\u8D27\u7269\u4F53\u79EF\u6700\u5927\u503C\ua
freightCargoVolumeMax=9999000
#\u5FEB\u8FD0\u53F0\u8D26\u5F02\u6B65\u521D\u59CB\u5316\u65B9\u5F0F,\u662F\u5426\u4F7F\u7528PDQ,true:pdq,false:jmq
freightOrderBankSyncInitPdqSwitch=false
#\u5FEB\u8FD0\u53F0\u8D26\u662F\u5426\u540C\u6B65\u521D\u59CB\u5316\u5F00\u5173\uFF0Ctrue:\u540C\u6B65,false:\u5F02\u6B65
freightOrderBankSyncInitSwitch=true
#b2cKA\u5207\u91CF\u5546\u5BB6\u9752\u9F99\u4E1A\u4E3B\u53F7\u767D\u540D\u5355-\u5728\u767D\u540D\u5355\u5185\u7684\u9700\u8981\u5199\u5173\u8054\u5173\u7CFB\uFF0C\u5F53\u5168\u91CF\u9700\u8981\u65F6\u8D4B\u503C\u5168\u91CF\u6807\u8BC6"all"\uFF0C\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
kaAccountNoWhite=all
#c2b KA\u5207\u91CF\u5546\u5BB6\u9752\u9F99\u4E1A\u4E3B\u53F7\u767D\u540D\u5355-\u5728\u767D\u540D\u5355\u5185\u7684\u9700\u8981\u5199\u5173\u8054\u5173\u7CFB\uFF0C\u5F53\u5168\u91CF\u9700\u8981\u65F6\u8D4B\u503C\u5168\u91CF\u6807\u8BC6"all"\uFF0C\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
c2bKaAccountNoWhite=all
#b2c\u767E\u5DDD\u5E76\u884C\u5904\u7406\u5F00\u5173:100 100%\u5F00\u542F,0~100 \u90E8\u5206\u5F00\u542F,0\u5173\u95ED
b2cBatrixParallelProcessSwitch=0
#\u8BE2\u4EF7\u63A5\u53E3\u53D1\u9001\u8BE2\u4EF7\u8BB0\u5F55\u6D88\u606F\u5F00\u5173
sendExpressOrderEnquiryRecordSwitch=true
#\u5FEB\u8FD0\u63FD\u6536\u540E\u6539\u5740\u652F\u4ED8\u8D85\u65F6\u65F6\u95F4buffer
freightPayOutTimeBuffer=5
#\u63A5\u5355\u6301\u4E45\u5316\u8D85\u65F6\u5220\u9664\u5F00\u5173
createRepositoryTimeoutDelSwitch=false
#\u5FEB\u8FD0\u6821\u9A8C\u5546\u5BB6\u662F\u5426\u5F00\u901A\u6708\u7ED3
freightValidateMonthlyPaymentSwitch=false
#\u767E\u5DDDB2C\u91CD\u7F6E\u8C03\u7528\u9884\u5206\u62E3waybillNo\u590D\u5236\u903B\u8F91\u5F00\u5173 True\u5F00\u542F,False\u5173\u95ED
b2cResetPresortRpcFieldSwitch=false

#\u51B7\u94FEB2B\u8D27\u7269\u91CD\u91CF\u6700\u5C0F\u503C\uFF08\u5355\u4F4Dkg\uFF09
ccB2BCargoWeightMin=0
#\u51B7\u94FEB2B\u8D27\u7269\u91CD\u91CF\u6700\u5927\u503C\uFF08\u5355\u4F4Dkg\uFF09
ccB2BCargoWeightMax=9999000
#\u51B7\u94FEB2B\u8D27\u7269\u6570\u91CF\u6700\u5C0F\u503C
ccB2BCargoNumMin=0
#\u51B7\u94FEB2B\u8D27\u7269\u6570\u91CF\u6700\u5927\u503C
ccB2BCargoNumMax=50000
#\u51B7\u94FEB2B\u4FEE\u6539\u4EE3\u6536\u8D27\u6B3E\u5F00\u5173
ccb2b.modifyCodSwitch=false
#\u5FEB\u8FD0PosType\u7070\u5EA6\u5207\u91CF\u540D\u5355
freightPosTypeOrderNos=
#\u51B7\u94FEB2B\u6574\u8F66\u8D85\u65F6\u53D6\u6D88\u65F6\u95F4
ccB2BCancelPayTimeOutOrderBuffer=14400

#\u5FEB\u8FD0\u4F18\u60E0\u5238\u4F7F\u7528\u5BA2\u6237\u8BA2\u5355\u53F7\u5F00\u5173
freightCouponUseCustomerOrderNoSwitch=true

#\u5408\u540C\u7269\u6D41\u8D27\u7269\u91CD\u91CF\u6700\u5C0F\u4E0E\u6700\u5927\u503C
contractCargoWeightMin=0
contractCargoWeightMax=9999000
#\u5408\u540C\u7269\u6D41\u8D27\u7269\u4F53\u79EF\u6700\u5C0F\u4E0E\u6700\u5927\u503C
contractCargoVolumeMin=0
contractCargoVolumeMax=9999000
#\u5408\u540C\u7269\u6D41\u8D27\u7269\u6570\u91CF\u6700\u5C0F\u4E0E\u6700\u5927\u503C
contractCargoNumMin=0
contractCargoNumMax=50000
#\u538B\u6D4B\u662F\u5426\u4E22\u5F03\u5F00\u5173 true:\u4E22\u5F03\uFF1Bfalse:\u4E0D\u4E22\u5F03\u6B63\u5E38\u538B
yc.abandon.switch=false
#\u9884\u70ED\u8282\u70B9\u8DF3\u8FC7\u5F00\u5173true-\u8DF3\u8FC7\uFF1Bfalse:\u4E0D\u8DF3\u8FC7
yr.skip.switch=false
#\u6E2F\u6FB3\u7B56\u7565\u4E3A\u4EC5\u4FEE\u6539\u62A5\u5173\u6570\u636E\u65F6\u6240\u80FD\u4FEE\u6539\u7684\u5185\u5BB9\u767D\u540D\u5355--\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
expressHKMModifyWhite=goods,attachments,fileTag,clearanceMode,clearanceType,consigneeIdNo,consigneeIdName,consigneeIdType,consignorEnName,enCityName,enAddress,importerVatNumber,importerEoriNumber,importerIossNumber
#\u63FD\u6536\u524D\u4FEE\u6539\u7ED3\u7B97\u65B9\u5F0F\u65F6\u9700\u8981\u6821\u9A8C\u8BE2\u4EF7\u72B6\u6001\u7684systemCaller\u767D\u540D\u5355--\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u5272
modifyBeforePickedUpValidEnquirySystemCallerWhite=PDA,C-SYSTEM,collectDispatchBackend
#\u8BA2\u5355\u9000\u6B3E\u8BB0\u5F55\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.refund.express.order.flow.switch =true
#\u5408\u540C\u7269\u6D41\u7BB1\u53F7\u6821\u9A8C\u662F\u5426\u8D70eclp
contract.boxCode.check.eclp.switch=false
#pop\u552E\u540E\u5408\u5355\u5F00\u5173
pop.merge.order.switch=true
#\u5BF9\u79C1pin\u4EE3\u6263\u8D85\u65F6\u65F6\u95F4:\u5206\u949F
uepPinPayTradeExpiry=2
#\u662F\u5426\u68C0\u6D4BPOP\u96C6\u8FD0\u5355\u5F00\u5173
needCheckPopConsolidation.switch=true
#\u5408\u540C\u7269\u6D41\u5199\u53F0\u8D26\u4E8B\u4E1A\u90E8
contractOrderBankSupportDeptNos=EBU4418055066886
#\u8BA2\u5355\u91CD\u53D7\u7406\u8BB0\u5F55\u53D1\u9001\u5F00\u5173\u914D\u7F6E
send.reaccept.express.order.flow.switch=true
#\u96F6\u552E\u8BA2\u5355\u5FEB\u8FD0\u6267\u884C\u5E97\u94FA\u6821\u9A8C
jdOrderValidateFreightPopIdSwitch=false
#\u5408\u540C\u7269\u6D41\u5546\u5BB6\u5355\u53F7eclp\u6821\u9A8C\u5F00\u5173
contract.orderNo.check.eclp.switch=false
#\u5FEB\u8FD0\u6574\u8F66\u76F4\u8FBE\u5F85\u9500\u552E\u786E\u8BA4\u65F6\u95F4
freightFTLWaitSalesConfirmTimeBuffer=15
#\u6E2F\u6FB3\u7A0E\u91D1\u7EC8\u7AEF\u7070\u5EA6\u7AD9\u70B9\u767D\u540D\u5355\u914D\u7F6E
taxPDAEndStationWhite=841276
#\u9006\u5411\u5355\u83B7\u53D6\u8FD0\u5355\u53F7\u5207\u6362\u5F00\u5173\uFF1Atrue\uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u5173\u95ED
reverseWaybillNoSwitch=true
#\u9884\u5206\u62E3\u62E6\u622A\u5207\u6362\u5F00\u5173\uFF1Atrue\uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u5173\u95ED
presortInterceptTypeSwitch = true
#\u6539\u5740\u6570\u636E\u540C\u6B65\u8DF3\u8FC7
readdressWaybillUpdateSwitch=true
#\u5FEB\u8FD0C2C\u6574\u8F66\u76F4\u8FBE\u652F\u4ED8\u8D85\u65F6\u65F6\u95F4
freightC2CFTLPayOutTimeBuffer=60
#\u5FEB\u8FD0C2C\u6574\u8F66\u76F4\u8FBE\u5F85\u5546\u5BB6\u786E\u8BA4\u65F6\u95F4
freightC2CFTLWaitConfirmTimeBuffer=50
#\u5FEB\u8FD0\u6574\u8F66\u76F4\u8FBE\u7684\u51E0\u4E2AtimeBuffer\u4F7F\u7528\u7684\u65F6\u95F4\u5355\u4F4D
timeUnitOfFreightFTLTimeBuffer=minutes
##\u8D27\u54C1\u660E\u7EC6\u884C\u6570\u6700\u5927\u503C
maxCargoListSize=20000
#\u5141\u8BB8\u5408\u5E76\u65B0\u65E7\u4EA7\u54C1\u7684\u4E1A\u52A1\u8EAB\u4EFD
allowProductsMergeBusinessUnits=cn_jdl_b2c,cn_jdl_c2c,cn_jdl_c2b,cn_jdl_o2o,cn_jdl_lm-b2c,cn_jdl_freight-service,cn_jdl_freight-consumer,cn_jdl_las,cn_jdl_cc-b2c,cn_jdl_cc-b2b,cn_jdl_contract,cn_jdl_tms-zx,cn_jdl_uep-c2b,cn_jdl_uep-c2c
#\u8C03\u7528\u4EA7\u54C1\u6620\u5C04\u5F00\u5173true:\u5F00\u542F(\u9ED8\u8BA4),false:\u5173\u95ED
invokeProductMappingSwitch=true
#\u5408\u540C\u7269\u6D41\u63A5\u5355\u4E0E\u4FEE\u6539\u6700\u5927\u7BB1\u53F7\u6570
contractBoxCodeMax=1000
#????????????????????????
switchFlowRunningOnlyList=
#\u5E7F\u64AD\u6D88\u606F\u8F6C\u6362\u65F6\u662F\u5426\u65E0\u6761\u4EF6\u5E7F\u64ADpresortExtend
broadcastConverterPresortExtendSwitch=true
#\u5FEB\u8FD0\u540E\u6B3E\u652F\u4ED8\u80FD\u5426\u53D6\u6D88\u670D\u52A1\u8BE2\u4EF7\u5355\u5F00\u5173
cashOnDeliveryCancelServiceEnquirySwitch=false
#LAS ASYNC send ORDER_STATUS_NOTICE
lasAsyncSendJMQ=true
#\u4E0D\u540C\u6B65\u8FD0\u5355\u5C5E\u6027\u4FE1\u606F\u5F00\u5173
notUpdatePackagingAttributesSwitch=false
#\u8C03\u7528\u8FD0\u5355\u53F7\u91CA\u653E\u5F00\u5173\u3002true:\u5F00\u542F(\u9ED8\u8BA4),false:\u5173\u95ED
waybillNoReleaseSwitch=true
# anti repeat lock switch for UEP merged order
mergedOrderAntiRepeat=true
#\u5FEB\u8FD0\u4E0B\u5355\u662F\u5426\u6821\u9A8C\u4E8B\u4E1A\u90E8\u542F\u7528\u72B6\u6001
freightValidateDepartmentStatusSwitch=false
#\u662F\u5426\u6821\u9A8C\u4E91\u4ED3vmi\u8D26\u53F7
clpsVmiCustomerConfigSwitch=false
# pay time out buffer for Document Send (minutes)
documentSendPayTimeout=120
#\u5FEB\u8FD0C2C\u6574\u8F66\u76F4\u8FBE\u53D6\u6D88\u8BA2\u5355\u662F\u5426\u81EA\u52A8\u6838\u9500
freightC2CFTLAutoWriteOffSwitch=false
#\u81EA\u52A8\u6838\u9500\u4E8B\u4E1A\u90E8\u7F16\u7801
autoWriteOffAccountNos=EBU4418046558556
## readdress preCheck addOnProduct BlackList
readdressAddOnProductBlackList=ed-a-0001,ed-a-0022,ed-a-0008,ed-m-0017,ed-a-0090,fr-a-0076,ed-a-0097
## modify record limit max value
modifyRecordLimitValue=150
#C2C\u5F02\u6B65\u8BE2\u4EF7\u53D1\u9001JMQ\u5207\u6362\u5F00\u5173\uFF1Atrue-JMQ\uFF1Bfalse-PDQ\uFF1B\u9ED8\u8BA4false
c2cAsyncEnquiryJmqSwitch=false
#B2C\u5F02\u6B65\u8BE2\u4EF7\u53D1\u9001JMQ\u5207\u6362\u5F00\u5173\uFF1Atrue-JMQ\uFF1Bfalse-PDQ\uFF1B\u9ED8\u8BA4false
b2cAsyncEnquiryJmqSwitch=false
## readdress order status allow whiteList
readdressOrderStatusWhiteList=10400,10500,10600
#replacing PDQ topic list
replacingPDQEnabledTopics=PLACEHOLDER_TOPIC
#\u653E\u5F00\u81EA\u884C\u8054\u7CFB\u63FD\u6536\u524D\u4FEE\u6539\u5361\u63A7
frightContactDirectlyInsOrDelBAP=false
# \u8C03\u7528\u591A\u5730\u5740\u6821\u9A8C\u5F00\u5173\u3002true:\u5F00\u542F(\u9ED8\u8BA4),false:\u5173\u95ED
multiAddressSwitch=true
#\u4ED3\u914D\u63A5\u914D\u9006\u5411\u5355\u76EE\u7684\u7AD9\u70B9\u4FE1\u4EFB\u5F00\u5173\uFF08true\uFF1A\u4FE1\u4EFB\uFF0Cfalse\uFF1A\u4E0D\u4FE1\u4EFB\uFF09
supplyChainEndStationTrustSwitch = true
#\u5FEB\u9012B2C\u5FEB\u8FD0B2C\u6B63\u5411\u63A5\u5355\uFF0C\u662F\u5426\u89E3\u6790\u5E76\u6821\u9A8C\u8D27\u54C1\u4FE1\u606F\u4E2D\u7684\u8FD0\u5355\u53F7
validateCargoWaybillCodeRuleSwitch=false
# \u5141\u8BB8COD\u4FEE\u65392\u6B21\u5F00\u5173
allowCodModifyTwoTimesSwitch=false
# \u5305\u88F9\u6A21\u5F0F\u9644\u52A0\u8D39\u5F00\u5173
packageModeAttachFeesSwitch=false
#\u5FEB\u8FD0\u8BE2\u4EF7\u65B0\u6D41\u7A0B\u5F00\u5173
freightNewEnquiryProcessSwitch=false
# \u5185\u90E8\u4FEE\u6539 \u5F00\u5173\u3002true:\u5F00\u542F,false:\u5173\u95ED(\u9ED8\u8BA4)
internalModifySwitch=false
#\u654F\u611F\u8BCD\u6821\u9A8C\uFF0C\u4FEE\u6539\u7B56\u7565\u767D\u540D\u5355,\u914D\u7F6E\u89C4\u5219\uFF1A[\u540D\u5355\u503C][][]
sensitiveWordsModifyRuleWhiteList=[outboundDelivery][outboundDeliveryCustom]
#\u7279\u6B8A\u6D41\u7A0B\uFF0C\u80FD\u529B\u8282\u70B9\u76F4\u63A5\u4ECE\u654F\u611F\u8BCD\u5230\u4E0B\u53D1\uFF0C\u8DF3\u8FC7\u4E2D\u95F4\u6D41\u7A0B\u8282\u70B9,\u4FEE\u6539\u7B56\u7565\u540D\u5355\uFF1A[\u540D\u5355\u503C][][]
specialFlowFromSensitiveWordsToIssueRuleList=[outboundDelivery][outboundDeliveryCustom]
#\u9632\u91CD\u964D\u7EA7\u4E1A\u52A1\u5355\u5143\u767D\u540D\u5355\uFF0C\u591A\u4E2A"[]"\u5206\u5272\uFF0CALL\u5168\u90E8
antiRepeatDownUnitWhiteList=[cn_jdl_c2c][cn_jdl_freight-consumer]
#\u521D\u59CB\u5316\u5199\u53F0\u8D26\u964D\u7EA7\uFF0C\u591A\u4E2A","\u5206\u5272\uFF0CALL\u5168\u90E8
initOrderBankWriteCacheDownUnitWhiteList=[]
#\u521D\u59CB\u5316\u8BFB\u53F0\u8D26\u964D\u7EA7\uFF0C\u591A\u4E2A","\u5206\u5272\uFF0CALL\u5168\u90E8
initOrderBankReadCacheDownUnitWhiteList=[cn_jdl_c2c][cn_jdl_c2b][cn_jdl_freight-consumer]
# \u8BA1\u7B97\u4EA7\u54C1\u7EBF\u5F00\u5173\u3002true:\u5F00\u542F,false:\u5173\u95ED(\u9ED8\u8BA4)
calcProductLineSwitch=false
#\u5FEB\u8FD0\u4EA7\u54C1\u6821\u9A8C\u548C\u9884\u5206\u62E3\u4F7F\u7528\u590D\u6838\u4FE1\u606F\u4EE3\u66FF\u8D27\u54C1\u4FE1\u606F\u5F00\u5173
freightProductPresortUseRecheckInfoSwitch=false
## \u9752\u9F99\u5168\u7A0B\u8DDF\u8E2A\u67E5\u8BE2 \u6743\u9650\u83B7\u53D6\u7CFB\u7EDF\u540D
waybill.trace.api.app.source=jdl-oms-express
# switch for ignore jingxi quantuo check
b2cExpressJingXiQuanTuoCheckSwitch=false
#\u7279\u6B8A\u6D41\u7A0B\u4ECE\u654F\u611F\u8BCD\u5230\u6301\u4E45\u5316\u8DF3\u8FC7\u4E2D\u95F4\u6D41\u7A0B\u8282\u70B9
specialFlowFromSensitiveWordsToRepositoryRuleList=[internalModifyLasConsigneeInfo][onlyModifyOrder][carbonEmissionCalculation]
#\u5FEB\u8FD0POS\u5230\u4ED8\uFF0CECard\u6253\u6807\u903B\u8F91\u8C03\u6574\u5F00\u5173\uFF0Ctrue\uFF0C\u65B0\u903B\u8F91\uFF0Cfalse\uFF0C\u8001\u903B\u8F91
freightPosDfECardSwitch = true
#\u5FEB\u8FD0\u6574\u8F66\u76F4\u8FBE\u62A5\u4EF7\u901A\u77E5CRM
freightEnquiryQuoteNoticeCRM=false
#\u63A5\u5355\u524D\u7F6E\u6821\u9A8C\uFF0C\u652F\u6301\u4E1A\u52A1\u8EAB\u4EFD\u767D\u540D\u5355\uFF1A[\u540D\u5355\u503C][][]
createPreCheckBusinessUnitList=[cn_jdl_b2c][cn_jdl_freight-service]
# switch zhengpinjianding order for ignore replace address
b2cZhengPinJianDingWaiveReplaceAddressSwitch=false
#\u4ED3\u914D\u8BA2\u5355\uFF0C\u6D3E\u9001\u9884\u5206\u62E3\u5B9E\u9645\u7F51\u7EDC\u7C7B\u578B\u4E3A\u5F3AB\uFF0C\u4E0D\u5904\u7406\u63FD\u6536\u9884\u5206\u62E3
presortSkipStartStationSwitch=true
#\u6570\u636E\u5E93\u9501\u51B2\u7A81\u662F\u5426\u7528UMP\u6253\u70B9\u8BA1\u6570
umpRegisterGetLockErrorSwitch=false
#\u8BE2\u4EF7\u6821\u9A8C\u652F\u4ED8\u6210\u529F\u5F00\u5173\u8DF3\u8FC7\u6E2F\u6FB3\u6761\u4EF6\u7684\u5F00\u5173:true\uFF0C\u8DF3\u8FC7,false\u4E0D\u8DF3\u8FC7
enquiryPaySuccessValidSkipHKMOSwitch=true
#\u8865\u5168\u662F\u5426\u53D1\u8D77\u8865\u7B7E
initReSignFlagSwitch=true
#\u6BD4\u5BF9\u6E20\u9053\u4FE1\u606F\u5F00\u5173\uFF1Atrue\uFF1A\u5F00\u542F\uFF0Cfalse\uFF1A\u5173\u95ED
compareChannelSwitch=true
#\u5FEB\u8FD0\u5BC4\u4ED8\u73B0\u7ED3\u91CD\u8D27\u4E0A\u697C\u662F\u5426\u8BC6\u522B\u6709\u65E0\u7535\u68AF
freightValidateUpstairsByElevatorSwitch=false
#\u5FEB\u9012B2C\u8DF3\u8FC7\u96F6\u552E\u5E97\u94FA\u6821\u9A8C\u5F00\u5173
skipValidateVenderIdSwitch=true
#\u4FEE\u6539\u53F0\u8D26\u5931\u8D25\u662F\u5426\u629B\u5F02\u5E38\uFF0Ctrue:\u629B\uFF1Bfalse\uFF1A\u4E0D\u629B
modifyOrderBankThrowExceptionSwitch=true
#C2C\u4FEE\u6539\u4E0D\u6821\u9A8C\u4EA7\u54C1\u8FD0\u8425\u6A21\u5F0F\u603B\u5F00\u5173
notCheckOperationModeForC2CModify=true
#\u4FEE\u6539\u573A\u666F\u5254\u9664\u539F\u5355\u8FD0\u8425\u6A21\u5F0F\uFF0C\u8C03\u4EA7\u54C1\u4E2D\u5FC3\u91CD\u65B0\u83B7\u53D6-\u4FEE\u6539\u7B56\u7565\u767D\u540D\u5355\uFF0C\u591A\u4E2A","\u5206\u5272\uFF0CALL\u5168\u90E8
operationModeDeleteProModifyRuleWhiteList=[beforePickUp]
#\u878D\u5408C2C\u5FEB\u9012\u4E3B\u4EA7\u54C1\uFF0C[\u540D\u5355\u503C][][]\uFF0CALL\u5168\u90E8
unitedC2CExpressMainProducts=[ed-m-0001][ed-m-0002][LL-HD-M][LL-SD-M]
#\u878D\u5408C2C\u5FEB\u8FD0\u4E3B\u4EA7\u54C1\uFF0C[\u540D\u5355\u503C][][]\uFF0CALL\u5168\u90E8
unitedC2CTransportMainProducts=[fr-m-0004]
#\u7981\u6B62\u4EA7\u54C1\u4E92\u6539\u7684\u589E\u503C\u670D\u52A1
unitedC2CExchangeProhibitAddonProducts=[fr-a-0001][ed-a-0009][LL-ZZ-DSHK][ed-a-0047][ed-a-0008][sendOnDemand]
jsf.ebsService.SelfPickTemporaryStorage.YtSrcSystem=test
#\u6743\u76CA\u83B7\u53D6\u9500\u552E\u5355\u53F7\u903B\u8F91\u5F00\u5173\uFF1Atrue\uFF1A\u5546\u54C1\u4FE1\u606F\uFF1Afalse\uFF1A\u5173\u8054\u5355
afterSalesNosSwitch=true
#\u8C03\u8BA1\u8D39\u8BE2\u4EF7\u662F\u5426\u4F20InquiryType\u5F00\u5173\uFF0Ctrue\uFF1A\u4F20\uFF0Cfalse\uFF1A\u4E0D\u4F20
enquiryExtendInquiryTypeSwitch=false
#\u7269\u8D44\u5468\u8F6C1\u62163\u65F6\uFF0C\u4E0D\u6267\u884C\u9884\u5206\u62E3\u6821\u9A8C\u5F00\u5173\uFF0Ctrue\uFF1A\u5F00\u542F\uFF0C\u4E0D\u6821\u9A8C\uFF0Cfalse\uFF1A\u6821\u9A8C
materialTurnoverPresortSwitch=true
#\u7269\u8D44\u5468\u8F6C\u662F\u5426\u5141\u8BB8\u4FEE\u6539\u5F00\u5173:true\uFF1A\u4E0D\u5141\u8BB8\u4FEE\u6539\uFF0Cfalse:\u5141\u8BB8\u4FEE\u6539\uFF0C\u9ED8\u8BA4true
b2cMaterialTurnoverNotAllowModifySwitch=true
#\u4EA7\u54C1\u6821\u9A8C\u540E\u56DE\u586B\u8DEF\u7531\u4EE3\u7801\u5230\u914D\u9001\u4FE1\u606F\u6269\u5C55\u5B57\u6BB5
backFillShipmentVrsProductCodeSwitch=true
#JDL\u8F6CDP\u65B0\u6D41\u7A0B\u5F00\u5173
jdlToDpSwitch=true
#\u62D2\u6536\u4E00\u5355\u5230\u5E95\u5141\u8BB8\u4FEE\u6539\u7684\u589E\u503C\u670D\u52A1\u767D\u540D\u5355
rejectReaddressAllowModifyProductWhiteList=ed-a-0010,LL-ZZ-QDFH,ed-a-0057,ed-a-0073,ed-a-0001,ed-a-0045,ed-a-0026
#\u7EC8\u7AEF\u4FEE\u6539\u6807\u8BC6\u521D\u59CB\u5316\u5F00\u5173:true,\u6301\u4E45\u5316\u80FD\u529B\u8BBE\u7F6E\u503C\uFF1Bfalse,\u521D\u59CB\u5316\u80FD\u529B\u8BBE\u7F6E\u503C
terminalModifyFlagInitSwitch=true
# Create Bank Fail Sync Cancel Order Switch For B2C
b2cCreateBankFailSyncCancelOrderSwitch=true
# moving PDQ to Batrix topic list
movingPdqToBatrixTopics=CREATE_ISSUE_RETRY
#\u4ED3\u914D\u63A5\u914D\u63A5\u8D27\u4ED3\u662F\u5426\u8DF3\u8FC7\u63FD\u6536\u9884\u5206\u62E3
supplyChainDeliverySkipStartStationPresort=true
#\u4ED3\u914D\u63A5\u914D\u8FD0\u8F93\u4E2D\u5305\u88F9\u6570\u91CF\u4E3A\u96F6\u62A5\u8B66
alarmIfSupplyOFCCargoQuantityZeroSwitch=true
#\u5927\u4EF6\u4E0B\u5355\u662F\u5426\u6302\u65B0\u5F02\u5E38
lasKaPreCreateHoldSwitch=true
#\u9001\u53D6\u540C\u6B65\u6D3E\u9001\u5355\u72B6\u6001\u9ED1\u540D\u5355
deliveryPickupSyncOrderStatusBlackList=19000,10600,10695,10700,10800
# createReplaceOrder error code switch
orderBankErrorCodeSwitch=true
#\u9001\u53D6\u540C\u6B65\u63A5\u5355\u6821\u9A8C\u5F00\u5173
deliveryPickupSyncValidateCreateSwitch=false
#\u9001\u53D6\u540C\u6B65\u4FEE\u6539\u6821\u9A8C\u5F00\u5173
deliveryPickupSyncValidateModifySwitch=false
# b2c check jd pop switch
b2cCheckJdPopSwitch=false
#basic cache switch
basic.data.switch=true
#\u6709\u6539\u5740\u8BB0\u5F55\u7684\u8BA2\u5355\u662F\u5426\u5141\u8BB8\u4FEE\u6539\u8BA2\u5355\u7ED3\u7B97\u65B9\u5F0F\uFF0Ctrue\uFF1A\u4E0D\u5141\u8BB8\u4FEE\u6539,false:\u5141\u8BB8\u4FEE\u6539
haveReaddressNotAllowModifySettSwitch = true
#\u5BF9\u6BD4\u5173\u8054\u5355\u5F00\u5173
compareRefOrderSwitch=true
#\u81EA\u8425\u8FD0\u5355\u4E0D\u5B58\u5728\u62A5\u8B66
deliveryPickupSyncJDRetailOrderNotExistAlarmSwitch=true
#\u9999\u6E2F\u6DF1\u5EA6\u5408\u4F5C\u81EA\u63D0\u67DC\u6821\u9A8C\u5F00\u5173
validateHKSelfPickupLockerSwitch=true
#\u9999\u6E2F\u6DF1\u5EA6\u5408\u4F5C\u81EA\u63D0\u67DC\u6821\u9A8C\u4EE3\u6536\u8D27\u6B3E\u5F00\u5173
validateHKSelfPickupLockerCODSwitch=true
#\u9999\u6E2F\u6FB3\u95E8\u81EA\u63D0\u5230\u4ED8\u6821\u9A8C
validateHKMOSelfPickupSettlementTypeSwitch=true
#\u5408\u540C\u7269\u6D41\u8BA2\u5355\u9632\u91CD\u548CECLP\u9632\u91CD\u987A\u5E8F\u8C03\u6574\u5F00\u5173\uFF0Ctrue\uFF1A\u5148\u6267\u884C\u8BA2\u5355\u9632\u91CD\uFF1Bfalse\uFF1A\u5148\u6267\u884CECLP\u9632\u91CD
contractAntiRepeatEclpSwitch=true
#\u51B7\u94FE\u9884\u5206\u62E3\u53D6\u5BA2\u6237\u5355\u53F7
setCustomerOrderNoToOrderIdSwitch=true
#\u72B6\u6001\u9006\u5E8F\u662F\u5426\u963B\u585E\u6D41\u7A0B\u4E1A\u52A1\u8EAB\u4EFD\u540D\u5355
statusReverseUnblockBusinessUnitList=ALL
#lq-a-0028\u6821\u9A8C\u6D41\u7A0B\u5F00\u5173
lasInfoCollectSwitch=true
#\u5FEB\u8FD0 C\u7F51\u8DF3\u8FC7\u5220\u9664\u7B49\u901A\u77E5\u6D3E\u9001\u5F00\u5173
skipDeleteWaitNoticeDeliverySwitch=true
#\u9006\u5411\u5355\u6216\u6539\u5740\u5355\u63A5\u5355\u5220\u9664\u539F\u5355\u589E\u503C\u4EA7\u54C1\u540D\u5355
reverseOrReaddressDeleteOriginalProductNoList=ed-a-0109
#\u654F\u611F\u8BCD\u6821\u9A8C\u5207\u6362,\u6309\u6E20\u9053\u6D41\u91CF\u6807\u8BC6\u5207\u6362\uFF0C true\uFF1A\u65B0\u6D41\u7A0B\uFF0Cfalse\uFF1A\u8001\u6D41\u7A0B \u9ED8\u8BA4\u65B0\u6D41\u7A0B
sensitiveWordsSwitchChannelFlag = true
#\u654F\u611F\u8BCD\u6821\u9A8C\u5207\u6362\uFF0C\u4E1A\u52A1\u8EAB\u4EFD\u540D\u5355\uFF0C\u547D\u4E2D\u540D\u5355\u65B0\u6D41\u7A0B\uFF0C\u5426\u5219\u8001\u6D41\u7A0B
sensitiveWordsSwitchBusinessUnitList=ALL
#\u654F\u611F\u8BCD\u6821\u9A8C\u5207\u6362SystemSubCaller\u540D\u5355\uFF0C \u547D\u4E2D\u540D\u5355\u65B0\u6D41\u7A0B\uFF0C\u5426\u5219\u8001\u6D41\u7A0B
sensitiveWordsSwitchSystemSubCallerList=ALL
#\u5FEB\u8FD0\u63FD\u6536\u524D\u5141\u8BB8\u4FEE\u6539\u5BC4\u4EF6\u4EBA\u5730\u5740\u7684\u5B50\u6E20\u9053\u540D\u5355
allowModifyConsignorAddressSystemSubCallerList=92
#\u5F02\u6B65\u4E0B\u53D1\u4E1A\u52A1\u8EAB\u4EFD\u540D\u5355
asyncIssueBusinessUnitList=[cn_jdl_b2c][cn_jdl_c2c][cn_jdl_c2b][cn_jdl_freight-service][cn_jdl_freight-consumer]
#\u5F02\u6B65\u4E0B\u53D1SystemCaller\u540D\u5355
asyncIssueSystemCallerList=ALL
#\u4FEE\u6539\u6301\u4E45\u5316\uFF0C\u4FEE\u6539\u4EA7\u54C1\u4FE1\u606FKEY\u589E\u52A0\u4EA7\u54C1\u7C7B\u578B\u5F00\u5173\uFF0Ctrue\u52A0\u4EA7\u54C1\u7C7B\u578B\uFF0Cfalse\u4E0D\u52A0\u4EA7\u54C1\u7C7B\u578B\uFF0C\u8001\u903B\u8F91
modifyRepositoryProductKeyAddProductType=true
#\u6C83\u5C14\u739B0\u5305\u88F9\u5F00\u5173\uFF1Atrue\uFF1A\u6821\u9A8C\uFF0Cfalse\uFF1A\u4E0D\u6821\u9A8C
walmartZeroPackageSwitch=true
#\u62CD\u62CD\u8D28\u68C0\u8BA2\u5355\u4E0D\u505A\u5730\u5740\u66FF\u6362\u5F00\u5173
b2cPaipaiCheckJdOrderSwitch=true
# switch for ignore jingxi quantuo check
freightB2cJingXiQuanTuoCheckSwitch=false
#\u51c6\u65f6\u4fdd\u65b0\u6d41\u7a0b\u5f00\u5173
onTimeGuaranteeProcessSwitch=true
#\u56de\u4f20\u65f6\u9700\u8981\u5e7f\u64ad\u4ea7\u54c1\u4fe1\u606f\u7684\u8fd0\u5355\u6267\u884c\u72b6\u6001
broadcastProductInfoExecutedStatusList=150,160,600,620
#\u662f\u5426\u652f\u6301\u4fee\u6539\u5e94\u6536\u5b9e\u6536\u7b56\u7565
modifyReceivableAndRealPaySwitch=true
#\u4fee\u6539\u5e94\u6536\u5b9e\u6536\u662f\u5426\u9700\u8981\u4fee\u6539\u6307\u7eb9\u6821\u9a8c
modifyReceivableAndRealPayFingerprintSwitch=true
#\u5fae\u4fe1\u89c6\u9891\u53f7\u9000\u6b3e\u5f00\u5173
applyRefundC2BWeChatVideoAccountSwitch=true