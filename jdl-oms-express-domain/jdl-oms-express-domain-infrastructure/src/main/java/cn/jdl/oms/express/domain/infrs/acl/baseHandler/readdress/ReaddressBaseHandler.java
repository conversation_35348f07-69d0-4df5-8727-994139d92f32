package cn.jdl.oms.express.domain.infrs.acl.baseHandler.readdress;

import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ChannelMapper;
import cn.jdl.oms.express.domain.infrs.acl.facade.track.BlueDragonServiceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FLowDirectionUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.DpDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReaddressAsyncEnquiryMsgDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 改址基本信息校验处理类
 */
@Component
public class ReaddressBaseHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ReaddressBaseHandler.class);

    /**
     * 查询青龙全程跟踪防腐层
     */
    @Resource
    private BlueDragonServiceFacade blueDragonServiceFacade;

    @Resource
    private IRedisClient redisClient;

    /**
     * 派送中改址校验
     *
     * @param orderModel       订单入参
     * @param originOrderModel 原始订单
     */
    public void deliveryReaddressValid(ExpressOrderModel orderModel, ExpressOrderModel originOrderModel) {
        OrderStatus orderStatus = originOrderModel.getOrderStatus();

        AtomicBoolean isNegotiateDeliveryAgain = new AtomicBoolean(false);
        // 跳过协商再投
        BatrixSwitch.applyByBoolean(BatrixSwitchKey.NEGOTIATION_REDELIVERY_READDRESS_VALID_SWITCH,
                (bTrue) -> {
                    //命中协商再投 直接跳过
                    if (OrderConstants.YES_VAL.equals(orderModel.getAttachment(OrderConstants.IS_NEGOTIATE_DELIVERY_AGAIN))) {
                        LOGGER.info("改址一单到底-派送中改址-处理协商再投校验-命中");
                        isNegotiateDeliveryAgain.set(true);
                    }
                },
                (cFalse) -> {
                    LOGGER.info("改址一单到底-派送中改址-不处理协商再投校验");
                });

        if (isNegotiateDeliveryAgain.get()) {
            LOGGER.info("改址一单到底-派送中改址-处理协商再投校验-命中-跳过后续处理");
            return;
        }

        BatrixSwitch.applyByBoolean(BatrixSwitchKey.READDRESS_TRACK_STATE_VALID_SWITCH,
                (bTrue) -> {
                    //改址前置校验接口中，调用全程跟踪接口根据指定操作单号查全程跟踪（对内）记录，当全程跟踪记录包含以下节点则不允许改址：
                    //自提上架【有过】：120
                    //派发众包【有过】：610、-3000
                    List<String> deliveryTrackStateBlackList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.DELIVERY_READDRESS_TRACK_STATE_BLACKLIST, ",");
                    List<String> trackStateListByWayBillNo = blueDragonServiceFacade.getTrackStateListByWayBillNo(originOrderModel.getCustomOrderNo());
                    List<String> deliveryTrackStateCheckFailList = trackStateListByWayBillNo.stream().filter(deliveryTrackStateBlackList::contains).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(deliveryTrackStateCheckFailList)) {
                        String forbidReaddressDesc = "运单全程跟踪状态节点为:" + deliveryTrackStateCheckFailList + "不允许改址";
                        LOGGER.error(forbidReaddressDesc);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("当前状态不允许改址");
                    }
                },
                (cFalse) -> {
                    LOGGER.info("改址一单到底-全程跟踪状态节点校验-开关关闭");
                });

        BatrixSwitch.applyByBoolean(BatrixSwitchKey.DELIVERY_READDRESS_VALID_SWITCH,
                (bTrue) -> {
                    if (orderStatus != null && OrderStatusEnum.ORDER_DELIVERY == orderStatus.getOrderStatus()) {
                        //有以下增值服务，派送中不允许改址的
                        //改址前置校验接口中，当产品信息包含以下增值产品的不允许改址：
                        //送货入仓- 「ed-a-0045」
                        //签单返还- 「ed-a-0010」
                        // 5 readdress1order2endV2 增值服务黑名单
                        List<String> deliveryProductBlackList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.DELIVERY_READDRESS_ADD_ON_PRODUCT_BLACKLIST, ",");
                        List<String> deliveryProductCheckFailList = originOrderModel.getProductDelegate().getProductList().stream().map(IProduct::getProductNo).filter(deliveryProductBlackList::contains).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(deliveryProductCheckFailList)) {
                            String productName = null != AddOnProductEnum.of(deliveryProductCheckFailList.get(0))
                                    ? AddOnProductEnum.of(deliveryProductCheckFailList.get(0)).getDesc()
                                    : null != ProductEnum.of(deliveryProductCheckFailList.get(0))
                                    ? ProductEnum.of(deliveryProductCheckFailList.get(0)).getDesc()
                                    : deliveryProductCheckFailList.get(0);
                            String forbidReaddressDesc = "订单存在产品:" + productName + ",不允许改址";
                            LOGGER.error(forbidReaddressDesc);
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                        }
                    }
                },
                (cFalse) -> {
                    LOGGER.info("改址一单到底-派送中改址校验-开关关闭");
                });
    }

    /**
     * 拦截状态校验
     * @param orderModel 入参
     */
    public void interceptStatusValid(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot){
        boolean interceptionThroughOrderModify = orderModel.isInterceptionThroughOrderModify();
        InterceptTypeEnum interceptType = orderSnapshot.getInterceptType();
        if(null != interceptType){
            if (interceptionThroughOrderModify){
                // 拦截状态判断
                List<String> readdressOrderInterceptTypeWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.INTERCEPT_READDRESS_INTERCEPT_TYPE_BLACK_LIST, ",");
                if (readdressOrderInterceptTypeWhiteList.contains(String.valueOf(interceptType.getCode()))) {
                    String forbidReaddressDesc = "原单拦截状态为:" + interceptType.getDesc() + ",不允许拦截改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }

            } else {
                // 原单拦截状态：原始订单状态【拦截中】、【拦截成功】、【解除拦截中】、【解除拦截失败】 不允许改址
                List<String> readdressOrderInterceptTypeWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ORDER_INTERCEPT_TYPE_BLACK_LIST, ",");
                if (readdressOrderInterceptTypeWhiteList.contains(String.valueOf(interceptType.getCode()))) {
                    String forbidReaddressDesc = "原单拦截状态为:" + interceptType.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }

            }
        }
    }

    /**
     * 拦截一单到底 总开关
     * @param orderModel 入参
     */
    public void interceptThroughOrderSwitch(ExpressOrderModel orderModel){
        boolean interceptionThroughOrderModify = orderModel.isInterceptionThroughOrderModify();
        if (interceptionThroughOrderModify){
            BatrixSwitch.applyByContainsOrAll(
                    BatrixSwitchKey.INTERCEPT_READDRESS_1ORDER2END_BUSINESS_UNIT_WHITE_LIST,
                    String.valueOf(orderModel.getOrderBusinessIdentity().getBusinessUnit()),
                    (bTrue) -> {
                    },(cFalse) -> {
                        LOGGER.error("业务身份：{} 不支持进行拦截一单到底操作", orderModel.getOrderBusinessIdentity().getBusinessUnit());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("当前订单不支持进行拦截一单到底操作");
                    });
        }
    }

    /**
     * 构建改址一单到底异步询价写账jmq消息体
     * @param context
     * @return
     */
    public ReaddressAsyncEnquiryMsgDto buildReaddressAsyncEnquiryJmqMsg(ExpressOrderContext context){
        ExpressOrderModel orderModel = context.getOrderModel();
        ReaddressAsyncEnquiryMsgDto readdressAsyncEnquiryMsgDto = new ReaddressAsyncEnquiryMsgDto();
        readdressAsyncEnquiryMsgDto.setRequestProfile(orderModel.requestProfile());
        readdressAsyncEnquiryMsgDto.setBusinessIdentity(orderModel.toBusinessIdentity());
        readdressAsyncEnquiryMsgDto.setOrderNo(orderModel.orderNo());
        readdressAsyncEnquiryMsgDto.setCustomOrderNo(orderModel.getCustomOrderNo());
        // 渠道信息
        readdressAsyncEnquiryMsgDto.setChannelInfo(ChannelMapper.INSTANCE.toChannelInfo(orderModel.getChannel()));
        // 操作人+操作人类型
        readdressAsyncEnquiryMsgDto.setOperator(orderModel.getOperator());
        if (null != orderModel.getInitiatorType()) {
            readdressAsyncEnquiryMsgDto.setInitiatorType(orderModel.getInitiatorType().getCode());
        }
        readdressAsyncEnquiryMsgDto.setExtendProps(orderModel.getExtendProps());
        readdressAsyncEnquiryMsgDto.setReaddress1Order2EndFirst(OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_1ORDER_2END_FIRST.getCode())));
        return readdressAsyncEnquiryMsgDto;
    }

    /**
     * 第一次改址月结改现结
     * @param context 上下文
     * @param order 订单数据
     * @return
     */
    public boolean firstReaddressMonth2Cash(ExpressOrderContext context, ExpressOrderModel order){
        boolean isFirstReaddress = OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_1ORDER_2END_FIRST.getCode()));
        LOGGER.debug("改址一单到底-首次改址,isFirstReaddress:{}",isFirstReaddress);
        boolean readdressSnapshotMonthSettle = SettlementTypeEnum.MONTHLY_PAYMENT == order.getFinance().getSettlementType();//原单是否月结
        LOGGER.debug("改址一单到底-首次改址-原单月结,readdressSnapshotMonthSettle:{}",readdressSnapshotMonthSettle);
        ModifyRecord lastThroughOrderModifyRecord = context.getModifyRecordDelegate().getLastThroughOrderModifyRecord();
        ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) lastThroughOrderModifyRecord.getModifyRecordDetail();
        boolean readdressNotMonth = null != modifyRecordDetail.getFinance()
                && !SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(modifyRecordDetail.getFinance().getSettlementType());
        LOGGER.debug("改址一单到底-首次改址-原单月结,改址现结，readdressNotMonth:{}", readdressNotMonth);
        boolean firstReaddressMonth2Cash = isFirstReaddress && readdressSnapshotMonthSettle && readdressNotMonth;
        if (firstReaddressMonth2Cash) {
            LOGGER.info("改址一单到底-首次改址-月结转现结");
        }
        return firstReaddressMonth2Cash;
    }

    /**
     * 月结订单改址校验
     *
     * @param orderModel 请求入参 订单
     * @return 月结订单改址校验
     * @韩敏茹 @齐鑫磊
     * https://joyspace.jd.com/pages/Y0NeXYidI6bfox6r2KgL
     * 第一次改成到付，后续只能现结（到付和现结）
     * 如果第一次月结，后续的支付方式只能是月结
     */
    public void monthSettleReaddressValid(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        LOGGER.info("改址一单到底-月结校验");
        Finance finance = orderModel.getFinance();
        Integer readdressSettlementType;
        if (null != finance && null != finance.getSettlementType()) {
            readdressSettlementType = finance.getSettlementType().getCode();
        } else {
            readdressSettlementType = snapshot.getFinance().getSettlementType().getCode();
        }
        if (null == readdressSettlementType) {
            LOGGER.warn("改址一单到底-未获取到改址结算方式，不处理月结校验");
            return;
        }

        String readdressTimes = Optional.ofNullable(snapshot.getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
        int readdressTimesVal = Integer.parseInt(readdressTimes);
        ModifyRecordDelegate modifyRecordDelegate = snapshot.getModifyRecordDelegate();
        //原单是月结
        if (SettlementTypeEnum.MONTHLY_PAYMENT == snapshot.getFinance().getSettlementType()) {
            //第一次不能寄付
            if (SettlementTypeEnum.CASH_ON_PICK.getCode().equals(readdressSettlementType)) {
                LOGGER.warn("改址一单到底-原单月结，不支持寄付现结");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("原单月结不支持寄付现结");
            }

            if (readdressTimesVal >= 1 && null != modifyRecordDelegate && CollectionUtils.isNotEmpty(modifyRecordDelegate.getModifyRecords())) {
                ModifyRecord originModifyRecord = modifyRecordDelegate.getOriginModifyRecord();
                if (null != originModifyRecord) {
                    ReaddressRecordDetailInfo originModifyRecordDetail = (ReaddressRecordDetailInfo) originModifyRecord.getModifyRecordDetail();
                    if (null != originModifyRecordDetail) {
                        //月结单改址校验
                        ModifyRecord firstEnabledThroughOrderModifyRecord = modifyRecordDelegate.getFirstEnabledThroughOrderModifyRecord();
                        if (null != firstEnabledThroughOrderModifyRecord) {
                            ReaddressRecordDetailInfo firstModifyRecordDetail = (ReaddressRecordDetailInfo) firstEnabledThroughOrderModifyRecord.getModifyRecordDetail();
                            if (null != firstModifyRecordDetail) {
                                //第一次改址
                                Integer firstReaddressSettlementType = firstModifyRecordDetail.getFinance().getSettlementType();
                                if (SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(firstReaddressSettlementType)) {
                                    if (!readdressSettlementType.equals(firstReaddressSettlementType)) {
                                        LOGGER.error("如果第一次月结，后续的支付方式只能是月结");
                                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                                .withCustom("改址操作，不支持的结算方式");
                                    }
                                } else {
                                    //第一次到付后不能再改
                                    LOGGER.error("改址一单到底-原单月结，到付改址后不允许再次改址，防止退款溢出");
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                            .withCustom("月结单到付改址后不支持再次改址");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 德邦落地配一单到底改址校验
     */
    public void dpDeliveryReaddressValid(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (!DpDeliveryOrderSignUtil.flag(orderModel)) {
            return;
        }

        // 有代物流公司收货款，不允许改址
        if (orderSnapshot.getProductDelegate() != null) {
            List<Product> codProducts = orderSnapshot.getProductDelegate().getCodProducts();
            if(CollectionUtils.isNotEmpty(codProducts)) {
                LOGGER.error("德邦落地配且有代物流公司收货款，不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配且有代物流公司收货款，不允许改址");
            }
        }

        // 卡控不能改结算方式和支付环节：寄付月结且后款支付。避免前端没卡住后续写台帐有问题
        if (orderModel.getFinance() != null) {
            Finance finance = orderModel.getFinance();
            // 卡控不能改原单结算方式和支付环节（=寄付月结+后款支付）
            if (finance.getSettlementType() != null && SettlementTypeEnum.MONTHLY_PAYMENT != finance.getSettlementType()) {
                LOGGER.error("德邦落地配，改址时不允许修改结算方式(settlementType)");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配，改址时不允许修改结算方式(settlementType)");
            }
            if (finance.getPaymentStage() != null && PaymentStageEnum.CASHONDELIVERY != finance.getPaymentStage()) {
                LOGGER.error("德邦落地配，改址时不允许修改支付环节(paymentStage)");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配，改址时不允许修改支付环节(paymentStage)");
            }

            // 卡控改址后必须是寄付月结+后款支付
            if (MapUtils.isNotEmpty(orderModel.getFinance().getExtendProps())) {
                // 新单财务域扩展信息
                Map<String, String> financeExt = orderModel.getFinance().getExtendProps();
                // 原单的结算方式
                String orderSettlementType = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
                if (StringUtils.isNotBlank(orderSettlementType) && !String.valueOf(SettlementTypeEnum.MONTHLY_PAYMENT.getCode()).equals(orderSettlementType)) {
                    LOGGER.error("德邦落地配，改址时不允许修改结算方式(originSettlementType)");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配，改址时不允许修改结算方式(originSettlementType)");
                }
                // 原单的支付环节
                String orderPaymentStage = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
                if (StringUtils.isNotBlank(orderPaymentStage) && !String.valueOf(PaymentStageEnum.CASHONDELIVERY.getCode()).equals(orderSettlementType)) {
                    LOGGER.error("德邦落地配，改址时不允许修改支付环节(originPaymentStage)");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配，改址时不允许修改支付环节(originPaymentStage)");
                }
            }
        }

        // 卡控不能收件人改址
        InitiatorTypeEnum initiatorType = orderModel.getInitiatorType();
        if (initiatorType != null && InitiatorTypeEnum.CONSIGNEE == initiatorType) {
            LOGGER.error("德邦落地配，不允许收件人发起改址");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配，不允许收件人发起改址");
        }
    }

    /**
     * 拒收一单到底，如果原单修改接口在执行中，不允许拒收换单
     */
    public void rejectReaddressModifyLockValidate(String tenantId, ExpressOrderModel orderModel) {
        OrderBusinessIdentity orderBusinessIdentity = orderModel.getOrderBusinessIdentity();
        String businessUnit = orderBusinessIdentity.getBusinessUnit();
        String businessType = orderBusinessIdentity.getBusinessType();
        String orderNo = orderModel.orderNo();

        String lockKey;
        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.ANTI_RE_LOCK_MODIFY_ENQUIRY_WHITE_LIST, businessUnit)) {
            LOGGER.info("命中防并发修改、询价公用锁");
            lockKey = tenantId +
                    ":" + orderNo;
        } else {
            LOGGER.info("未命中防并发修改、询价公用锁");
            lockKey = tenantId +
                    ":" + businessUnit +
                    ":" + businessType +
                    ":" + BusinessSceneEnum.MODIFY.getCode() +
                    ":" + orderNo;
        }
        if (redisClient.get(lockKey) != null) {
            LOGGER.info("校验失败，拒收一单到底改址锁，KEY：{}", lockKey);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_LOCK_FAIL)
                    .withCustom(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_LOCK_FAIL.desc());
        }
    }

    /**
     * 拒收一单到底，校验订单状态必须是拒收
     */
    public void rejectReaddressOrderStatusValid(ExpressOrderModel orderModel, ExpressOrderModel originOrderModel) {
        if (!orderModel.isRejectionOrder()) {
            return;
        }
        OrderStatus orderStatus = originOrderModel.getOrderStatus();
        if (OrderStatusEnum.CUSTOMER_REJECTED != orderStatus.getOrderStatus()) {
            LOGGER.error(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_ORDER_STATUS_VALIDATE_FAIL.desc());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_ORDER_STATUS_VALIDATE_FAIL).withCustom(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_ORDER_STATUS_VALIDATE_FAIL.desc());
        }
    }

    /**
     * 港澳订单改址校验
     * @param orderModel 当前单
     * @param isPrecheck 是否改址前置校验接口
     */
    public void hkmoReaddressValid(ExpressOrderModel orderModel, boolean isPrecheck) {
        // 非港澳订单不处理
        ExpressOrderModel originOrderModel = orderModel.getOrderSnapshot();
        if (originOrderModel == null || !originOrderModel.isHKMO()) {
            return;
        }

        // 非改址不处理：是否改址，以上游是否传改址增值服务来判断（改址前置校验不传产品，默认是改址，不return）
        if (!isPrecheck && (orderModel.getProductDelegate() == null || !orderModel.getProductDelegate().hasReaddressProduct())) {
            return;
        }

        // 卡控流向：只允许同城改址
        if (FLowDirectionUtils.FLowDirection.HKMO_INTRA_CITY != FLowDirectionUtils.getFLowDirection(originOrderModel.getCustoms())) {
            LOGGER.error("港澳订单改址，只允许同城改址");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，只允许同城改址");
        }

        // 卡控结算方式
        // 原单：C2C 寄付现结、到付现结；B2C 寄付月结、到付现结。并且不允许修改原单结算方式
        SettlementTypeEnum snapshotSettlementType = originOrderModel.getFinance().getSettlementType();
        if (originOrderModel.isC2C()
                && !(SettlementTypeEnum.CASH_ON_PICK == snapshotSettlementType || SettlementTypeEnum.CASH_ON_DELIVERY == snapshotSettlementType)) {
            LOGGER.error("港澳订单改址，快递C2C只支持结算方式(settlementType)为到寄付现结或付现结的订单");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，快递C2C只支持结算方式(settlementType)为到寄付现结或付现结的订单");
        }
        if (originOrderModel.isB2C()
                && !(SettlementTypeEnum.MONTHLY_PAYMENT == snapshotSettlementType || SettlementTypeEnum.CASH_ON_DELIVERY == snapshotSettlementType)) {
            LOGGER.error("港澳订单改址，快递B2C只支持结算方式(settlementType)为寄付月结或到付现结的订单");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，快递B2C只支持结算方式(settlementType)为寄付月结或到付现结的订单");
        }

        // 改址：C2C 到付现结；B2C 寄付月结、到付现结（改址前置校验不传财务信息，实际略过）
        if (orderModel.getFinance() != null) {
            Finance finance = orderModel.getFinance();
            SettlementTypeEnum settlementType = finance.getSettlementType();
            if (originOrderModel.isC2C()
                    && settlementType != null
                    && SettlementTypeEnum.CASH_ON_DELIVERY != settlementType) {
                LOGGER.error("港澳订单改址，快递C2C改址结算方式(settlementType)只能是到付现结");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，改址结算方式(settlementType)只能是到付现结");
            }
            if (originOrderModel.isB2C()
                    && settlementType != null
                    && !(SettlementTypeEnum.CASH_ON_DELIVERY == settlementType || SettlementTypeEnum.MONTHLY_PAYMENT == settlementType)) {
                LOGGER.error("港澳订单改址，快递B2C改址结算方式(settlementType)只能是到付现结或寄付月结");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，快递B2C改址结算方式(settlementType)只能是到付现结或寄付月结");
            }

            // 不允许修改原单结算方式
            if (MapUtils.isNotEmpty(orderModel.getFinance().getExtendProps())) {
                Map<String, String> financeExt = orderModel.getFinance().getExtendProps();
                String originSettlementType = financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey());
                if (StringUtils.isNotBlank(originSettlementType)
                        && !String.valueOf(snapshotSettlementType.getCode()).equals(originSettlementType)) {
                    LOGGER.error("港澳订单改址，不允许修改原单结算方式(originSettlementType)");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳订单改址，不允许修改原单结算方式(originSettlementType)");
                }
            }
        }
     }

    /**
     * 派送方式改为自提校验
     */
    public void selfPickupReaddressValid(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (!isChangeToSelfPickup(orderModel, orderSnapshot)) {
            return;
        }
        // 不能有代收货款
        if (orderModel.getProductDelegate() != null
                && !orderModel.getProductDelegate().getCodProducts().isEmpty()) {
            String forbidReaddressDesc = "存在代收货款增值服务，派送方式不允许改为送自提";
            LOGGER.error(forbidReaddressDesc);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
        }
        // 结算方式不能是到付
        String originSettlementType = getOriginSettlementType(orderModel);
        if (originSettlementType != null) {
            // 修改结算方式不为空，优先判断
            if (String.valueOf(SettlementTypeEnum.CASH_ON_DELIVERY.getCode()).equals(originSettlementType)) {
                String forbidReaddressDesc = "修改结算方式为到付现结，派送方式不允许改为送自提";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }
        } else {
            // 修改结算方式为空，从快照判断
            if (orderSnapshot.getFinance() != null
                    && SettlementTypeEnum.CASH_ON_DELIVERY == orderSnapshot.getFinance().getSettlementType()) {
                String forbidReaddressDesc = "结算方式为到付现结，派送方式不允许改为送自提";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }
        }
    }

    /**
     * 判断派送方式是否由非自提改为自提
     */
    private boolean isChangeToSelfPickup(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        if (orderModel.getShipment() == null
                || orderModel.getShipment().getDeliveryType() == null) {
            return false;
        }
        DeliveryTypeEnum deliveryType = orderModel.getShipment().getDeliveryType();
        DeliveryTypeEnum deliveryTypeSnapshot = orderSnapshot.getShipment().getDeliveryType();
        return DeliveryTypeEnum.SELF_PICKUP == deliveryType && DeliveryTypeEnum.SELF_PICKUP != deliveryTypeSnapshot;
    }

    /**
     * 获取修改原单结算方式
     */
    private String getOriginSettlementType(ExpressOrderModel orderModel) {
        return Optional.ofNullable(orderModel)
                .map(ExpressOrderModel::getFinance)
                .map(Finance::getExtendProps)
                .filter(MapUtils::isNotEmpty)
                .map(extendProps -> extendProps.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()))
                .orElse(null);
    }

    /**
     * 场景化寄件校验
     *
     * @param orderSnapshot
     */
    public void sceneDeliveryValid(ExpressOrderModel orderSnapshot) {
        if (null == orderSnapshot) {
            return;
        }
        if (orderSnapshot.isJG12123()) {
            LOGGER.error("场景化寄件-交管12123不允许改址");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("场景化寄件-交管12123不允许改址");
        }
    }
}