package cn.jdl.oms.express.domain.ability.init;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.CostInfoMapper;
import cn.jdl.oms.express.domain.extension.init.IInitExtension;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachFeeEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.utils.ContextInfoUtil;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.FingerprintUtil;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdl.product.enums.ClearanceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static cn.jdl.oms.express.shared.common.constant.OrderConstants.USER_CARGO_NAME;

/**
 * @Package： cn.jdl.oms.express.domain.ability.init
 * @ClassName: CreateInitAbility
 * @Description: 修改初始化能力
 * @Author： wangjingzhao
 * @CreateDate 2021/3/19 2:11 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@DomainAbility(name = "纯配修改领域能力-修改初始化活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.MODIFY, isDefault = false)
public class ModifyInitAbility extends AbstractDomainAbility<ExpressOrderContext, IInitExtension> {


    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyInitAbility.class);

    /**
     * 是否忽略修改次数的标示
     */
    public static final String IGNORE_MODIFY_FLAG = "ignoreModifyFlag";
    //"0"不忽略修改标识，"1"忽略修改标识
    public static final String IGNORE_MODIFY_FLAG_VALUE = "1";

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;


    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 修改初始化活动能力
     * <AUTHOR>
     * @createDate 2021/3/19 2:17 下午
     * @lastModify
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) throws DomainAbilityException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("纯配修改领域能力-初始化执行开始");
            // 根据标位判断扩展点是否执行过
            // 改址一单到底需要重复调用初始化能力
            /*if (null != expressOrderContext.getExtInfo(this.getClass().getName())) {
                LOGGER.info("初始化能力重复执行");
                return;
            }*/

            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            if (StringUtils.isBlank(orderModel.requestProfile().getTraceId())) {
                LOGGER.error("纯配修改初始化校验异常,traceId不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withVars("traceId不能为空");
            }
            if (StringUtils.isBlank(orderModel.requestProfile().getTenantId())) {
                LOGGER.error("纯配修改初始化校验异常,tenantId不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withVars("tenantId不能为空");
            }
            //不修改modifyTime的场景打忽律标识
            if(notModifyTims(orderModel)){
                Map<String, String> map = orderModel.getExtendProps();
                if(MapUtils.isEmpty(map)){
                    map = new HashMap<>();
                }
                map.put(IGNORE_MODIFY_FLAG,IGNORE_MODIFY_FLAG_VALUE);
                orderModel.setExtendProps(map);
            }
            IInitExtension extension = this.getMiddleExtensionFast(IInitExtension.class,
                    expressOrderContext.getOrderModel(),
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            LOGGER.info("纯配修改领域能力-初始化执行开始");
            if (extension != null) {
                extension.execute(expressOrderContext);
            }
            // 上下文增加标位
            expressOrderContext.putExtMaps(this.getClass().getName(), StringUtils.EMPTY);
            // 新增改址一单到底标识 置于上下文 用于后续流程判断
            if(orderModel.isReaddress1Order2End() || orderModel.isFreightJFInterceptionThroughOrderModify()){
                expressOrderContext.putExtMaps(ContextInfoEnum.READDRESS_1ORDER_2END.getCode(), OrderConstants.YES_VAL);
            }

            // 新增拦截一单到底标识 置于上下文 用于后续流程判断
            if(orderModel.isInterceptionThroughOrderModify()){
                expressOrderContext.putExtMaps(ContextInfoEnum.INTERCEPTION_THROUGH_ORDER_MODIFY.getCode(), OrderConstants.YES_VAL);
            }

            // 新增拦截一单到底标识 置于上下文 用于后续流程判断
            if(orderModel.isFreightJFInterceptionThroughOrderModify()){
                expressOrderContext.putExtMaps(ContextInfoEnum.FREIGHT_JF_INTERCEPTION_THROUGH_ORDER_MODIFY.getCode(), OrderConstants.YES_VAL);
            }

            // 内部修改 初始化。
            initInternalModify(expressOrderContext);

            // 初始化特殊流程标识
            initSpecialFlow(expressOrderContext);

            if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.TERMINAL_MODIFY_FLAG_INIT_SWITCH)) {
                // 初始化切量标识
                if (InitiatorTypeEnum.SHIPPER == orderModel.getInitiatorType()) {
                    Map<String, String> channelExt = orderModel.getChannel().getExtendProps();
                    if (channelExt == null) {
                        channelExt = new HashMap<>();
                    }
                    channelExt.put(AttachmentKeyEnum.TERMINAL_MODIFY_FLAG.getKey(), OrderConstants.YES_VAL);
                    orderModel.getChannel().setExtendProps(channelExt);
                }
            }

            // 修改场景【防拒单】主产品剔除运营模式
            operationModeDeletePro(expressOrderContext);

            // 芝麻代扣修改标识 置于上下文 用于后续流程判断
            ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
            if (PaymentTypeEnum.ifAliPay(orderModel.getFinance().getPayment())
                    && !PaymentTypeEnum.ifAliPay(snapshot.getFinance().getPayment())) {
                // 1-从无到有，非芝麻代扣改为芝麻代扣
                expressOrderContext.putExtMaps(ContextInfoEnum.PAYMENT_TYPE_ALIPAY_CHANGE_FLAG.getCode(), OrderConstants.YES_VAL);
                LOGGER.info("「非芝麻代扣」改为「芝麻代扣」");
            } else if (((null != orderModel.getFinance().getPayment()
                    && !PaymentTypeEnum.ifAliPay(orderModel.getFinance().getPayment()))
                    || orderModel.isFieldClear(ModifyItemConfigEnum.PAYMENT.getCode()))
                    && PaymentTypeEnum.ifAliPay(snapshot.getFinance().getPayment())) {
                // 0-从有到无，芝麻代扣改为非芝麻代扣
                expressOrderContext.putExtMaps(ContextInfoEnum.PAYMENT_TYPE_ALIPAY_CHANGE_FLAG.getCode(), OrderConstants.NO_VAL);
                LOGGER.info("「芝麻代扣」改为「非芝麻代扣」");
            }

            // 收寄件人操作修改后，更新用户侧货品名称
            if (InitiatorTypeEnum.CONSIGNOR == orderModel.getInitiatorType() || InitiatorTypeEnum.CONSIGNEE == orderModel.getInitiatorType()) {
                if (null != orderModel.getCargoDelegate()
                        && CollectionUtils.isNotEmpty(orderModel.getCargoDelegate().getCargoList())) {
                    List<Cargo> cargoList = (List<Cargo>) orderModel.getCargoDelegate().getCargoList();
                    for (Cargo cargo : cargoList) {
                        if (StringUtils.isBlank(cargo.getCargoName())) {
                            continue;
                        }
                        if (cargo.getExtendProps() == null) {
                            cargo.setExtendProps(new HashMap<>());
                        }
                        cargo.getExtendProps().put(USER_CARGO_NAME, cargo.getCargoName());
                    }
                }
            }

            // 初始化报关自定义托寄物 -- 后续询价写账退款模式
            // 只有港澳出口业务设计
            if (ModifySceneRuleConstants.CSS_CUSTOMS_DECLARATION_AUDIT.equals(ModifySceneRuleUtil.getModifySceneRule(orderModel))
                    && snapshot.isExportHKMO() && null != orderModel.getCustoms()) {
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.CSS_AUDIT_CUSTOMS_SWITCH)) {
                    // 初始化报关自定义托寄物
                    initCssCustomsAuditScene(expressOrderContext, orderModel, snapshot);
                } else {
                    LOGGER.info("开关关闭");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("港澳自定义托寄物场景暂不支持");
                }
            }

            LOGGER.info("纯配修改领域能力-初始化活动执行结束");
        } catch (AbilityExtensionException e) {
            LOGGER.info("纯配修改领域能力-初始化执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.info("纯配修改领域能力-初始化执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 根据订单的海关审核状态和清关类型，处理不同的财务场景（询价补款、退款、修改台账等）
     * @param context 订单上下文，用于存储处理过程中的扩展信息
     * @param orderModel 当前订单模型
     * @param snapshot 订单快照
     */
    private void initCssCustomsAuditScene(ExpressOrderContext context, ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        // 客服审核修改策略 & 港澳出口业务
        String status = MapUtils.getString(orderModel.getCustoms().getExtendProps(), OrderConstants.CSS_AUDIT_STATUS);
        // 客服提交场景
        if (OrderConstants.ONE.equals(status)) {
            LOGGER.info("识别报关方式：");
            // 清关类型
            String clearanceMode = orderModel.getCustoms().getClearanceMode();
            // 原单是否包含附加费
            boolean containsQGF = snapshot.getFinance().containsAttachFee(AttachFeeEnum.HM_QGFWF.getCode());
            // 结算方式
            SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(orderModel);
            String scene = null;
            // WARN 此处只涉及揽收后
            if (ClearanceEnum.FORMAL.getCode().equals(clearanceMode) && !containsQGF) {
                // 场景：当前为正式，如果原单附加费存在报关附加费，则不处理；如果不存在则需要询价补款
                if (SettlementTypeEnum.CASH_ON_PICK == settlementType) {
                    // 寄付走生成服务询价单先款询价
                    scene = ContextInfoEnum.CUSTOMS_ONLINE_PAY.getCode();
                    context.putExtMaps(ContextInfoEnum.CUSTOMS_ONLINE_PAY.getCode(), OrderConstants.YES_VAL);
                } else {
                    if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
                        // 到付走修改询价写账
                        scene = ContextInfoEnum.CUSTOMS_NORMAL.getCode();
                        context.putExtMaps(ContextInfoEnum.CUSTOMS_NORMAL.getCode(), OrderConstants.YES_VAL);
                    }
                    // 处理附加费
                    List<CostInfo> attachFees = snapshot.getFinance().getAttachFees();
                    List<CostInfo> updatedFees = CostInfoMapper.INSTANCE.copyVoList(attachFees);
                    CostInfo customs = new CostInfo();
                    customs.setCostNo(AttachFeeEnum.HM_QGFWF.getCode());
                    customs.setCostName(AttachFeeEnum.HM_QGFWF.getDesc());
                    updatedFees.add(customs);
                    orderModel.getFinance().setAttachFees(updatedFees);
                    // 更新modifiedFields
                    Map<String, String> modifiedFields = new HashMap<>();
                    modifiedFields.put(ModifiedFieldEnum.ATTACH_FEES.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
                    orderModel.appendModifiedFields(modifiedFields);
                }
            } else if (ClearanceEnum.SIMPLIFIED.getCode().equals(clearanceMode) && containsQGF) {
                // 场景：当前为简易，如果原单附加费存在报关附加费，则退款/修改台账；如果不存在则不处理
                if (SettlementTypeEnum.CASH_ON_PICK == settlementType) {
                    // 寄付走退款
                    scene = ContextInfoEnum.CUSTOMS_REFUND.getCode();
                    context.putExtMaps(ContextInfoEnum.CUSTOMS_REFUND.getCode(), OrderConstants.YES_VAL);
                } else {
                    if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
                        // 到付走修改询价写账
                        scene = ContextInfoEnum.CUSTOMS_NORMAL.getCode();
                        context.putExtMaps(ContextInfoEnum.CUSTOMS_NORMAL.getCode(), OrderConstants.YES_VAL);
                    }
                    // todo 处理附加费
                    List<CostInfo> attachFees = snapshot.getFinance().getAttachFees();
                    List<CostInfo> updatedFees = new ArrayList<>();
                    for (CostInfo attachFee : attachFees) {
                        if (AttachFeeEnum.HM_QGFWF.getCode().equals(attachFee.getCostNo())) {
                            continue;
                        }
                        // 深拷贝
                        updatedFees.add(CostInfoMapper.INSTANCE.copy(attachFee));
                    }
                    if (updatedFees.isEmpty()) {
                        // 更新后附加费为空 说明是全量删除
                        Map<String, String> modifiedFields = new HashMap<>();
                        modifiedFields.put(ModifiedFieldEnum.ATTACH_FEES.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());
                        orderModel.appendModifiedFields(modifiedFields);
                    } else {
                        orderModel.getFinance().setAttachFees(updatedFees);
                        // 更新modifiedFields
                        Map<String, String> modifiedFields = new HashMap<>();
                        modifiedFields.put(ModifiedFieldEnum.ATTACH_FEES.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
                        orderModel.appendModifiedFields(modifiedFields);
                    }

                }
            }
            LOGGER.info("识别报关方式结束: {}", scene);
        }
    }

    /**
     * 内部修改 初始化。
     *
     * @param expressOrderContext
     */
    private void initInternalModify(ExpressOrderContext expressOrderContext) {

        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        // 非内部修改场景，跳过
        if (!ModifySceneRuleUtil.isPartOfInternalModify(orderModel)) {
            return;
        }

        // 属于内部修改操作再记打点。此告警需重点关注
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".initInternalModify"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);

        // 设置 内部修改 属性 extMaps.internalModify。会跳过非必要的能力点，置于上下文，用于后续流程判断
        try {

            if (expressUccConfigCenter.isInternalModifySwitch()) {
                if (fingerprintCheckPassed(orderModel)) {
                    // 设置上下文
                    ContextInfoUtil.setupInternalModify(expressOrderContext);
                } else {
                    LOGGER.error("内部修改指纹错误");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withVars("内部修改指纹错误");
                }
            } else {
                LOGGER.error("内部修改未授权");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withVars("内部修改未授权");
            }

        } catch (Exception e) {

            Profiler.functionError(callerInfo);
            LOGGER.error("内部修改异常: ", e);
            throw e;

        } finally {

            Profiler.registerInfoEnd(callerInfo);

        }

    }

    /**
     * 指纹校验是否通过
     *
     * @param orderModel
     * @return
     */
    private boolean fingerprintCheckPassed(ExpressOrderModel orderModel) {

        // 先取订单号，没有取运单号
        String digest = orderModel.orderNo();
        if (StringUtils.isBlank(digest)) {
            digest = orderModel.getCustomOrderNo();
        }

        digest = FingerprintUtil.generate(digest);

        boolean passed = digest.equalsIgnoreCase(orderModel.getAttachment(OrderConstants.INTERNAL_MODIFY_FINGERPRINT));

        // 校验成功，清理指纹
        if (passed) {
            orderModel.removeAttachment(OrderConstants.INTERNAL_MODIFY_FINGERPRINT);
        }

        return passed;
    }

    @Override
    public IInitExtension getDefaultExtension() {
        return null;
    }

    /**
     * 不修改modifyTime的场景
     * 1 仅修改报关数据修改策略忽略modifyTime标识
     * @param model
     * @return
     */
    public boolean notModifyTims(ExpressOrderModel model){
        Map<String, String> extendProps = model.getExtendProps();
        if (MapUtils.isNotEmpty(extendProps)) {
            String modifySceneRule = extendProps.get(ModifySceneRuleConstants.MODIFY_SCENE_RULE);
            if ( null != modifySceneRule && ModifySceneRuleConstants.ONLY_MODIFY_CUSTOMS.equals(modifySceneRule)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 初始化特殊流程标识
     *
     * @param context
     */
    private void initSpecialFlow(ExpressOrderContext context){
        String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(context.getOrderModel());
        BatrixSwitch.applyByContainsOrAll(
                BatrixSwitchKey.SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_ISSUE_RULE_LIST,
                modifySceneRule,
                (bTrue) -> {
                    //特殊流程，能力节点直接从敏感词到下发，跳过中间流程节点
                    context.putExtMaps(ContextInfoEnum.SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_ISSUE.getCode(), OrderConstants.YES_VAL);
                    LOGGER.info("modifySceneRule={},能力节点直接从敏感词到下发，跳过中间流程节点", modifySceneRule);
                    return;
                });
        BatrixSwitch.applyByContainsOrAll(
                BatrixSwitchKey.SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_REPOSITORY_RULE_LIST,
                modifySceneRule,
                (bTrue) -> {
                    //特殊流程，能力节点直接从敏感词到持久化，跳过中间流程节点
                    context.putExtMaps(ContextInfoEnum.SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_REPOSITORY.getCode(), OrderConstants.YES_VAL);
                    LOGGER.info("modifySceneRule={},能力节点直接从敏感词到持久化，跳过中间流程节点", modifySceneRule);
                });
    }

    /**
     * 修改场景剔除路由运营模式处理
     * @param expressOrderContext
     */
    private void operationModeDeletePro(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        // 揽收后是否剔除
        // 开-揽收剔除
        // 关-揽收后不剔除
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.AFTER_PICKUP_DELETE_OPERATION_MODE_SWITCH)) {
            LOGGER.info("修改场景-剔除主产品运营模式-订单揽收校验-开关开-无需判断揽收状态-剔除");
        } else {
            if(orderSnapshot.getOrderStatus().isAfterPickedUp()){
                LOGGER.info("修改场景-剔除主产品运营模式-订单已揽收-不剔除");
                return;
            }
        }

        String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(orderModel);
        if (StringUtils.isBlank(modifySceneRule)) {
            LOGGER.info("修改场景-剔除主产品运营模式-修改策略为空-不处理路由运营模式剔除");
            return;
        }
        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.OPERATION_MODE_DELETE_PRO_MODIFY_RULE_WHITE_LIST, modifySceneRule)) {
            LOGGER.info("修改场景-剔除主产品运营模式-修改策略在白名单-处理路由运营模式剔除");
        } else {
            LOGGER.info("修改场景-剔除主产品运营模式-修改策略不在白名单-不处理路由运营模式剔除");
            return;
        }

        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.OPERATION_MODE_DELETE_PRO_UNIT_WHITE_LIST,
                orderModel.getOrderBusinessIdentity().getBusinessUnit())) {
            LOGGER.info("修改场景-剔除主产品运营模式-命中业务身份白名单-处理路由运营模式剔除");
        } else {
            LOGGER.info("修改场景-剔除主产品运营模式-未命中业务身份白名单-不处理路由运营模式剔除");
            return;
        }

        AtomicReference<Boolean> isSupplyChainBreak = new AtomicReference<>(Boolean.FALSE);
        //仓配接配处理开关
        BatrixSwitch.applyByBoolean(BatrixSwitchKey.SUPPLY_CHAIN_DELIVERY_DELETE_OPERATION_MODE_SWITCH,
                (bTrue) -> {
                    LOGGER.info("修改场景-仓配接配-剔除主产品运营模式-校验开启");
                    if ((MagicCommonConstants.STRING_1.equals(orderSnapshot.getOrderSignVal(OrderSignEnum.SUPPLY_CHAIN_DELIVERY.getCode())))
                            || (null != orderSnapshot.getChannel() && SystemCallerEnum.SUPPLY_OFC == orderSnapshot.getChannel().getSystemCaller())) {
                        LOGGER.info("修改场景-仓配接配-剔除主产品运营模式-校验开启-命中供应链OFC订单，不需要剔除路由运营模式");
                        isSupplyChainBreak.set(Boolean.TRUE);
                    }
                },
                (cFalse) -> {
                    LOGGER.info("修改场景-仓配接配-剔除主产品运营模式-校验关闭-需要剔除路由运营模式");
                });

        if(isSupplyChainBreak.get()){
            LOGGER.info("修改场景-仓配接配-剔除主产品运营模式-校验开启-且命中供应链OFC订单-无需剔除路由运营模式");
            return;
        }

        LOGGER.info("修改场景-剔除主产品运营模式，准备剔除");
        // 产品信息有变更，从入参产品信息剔除路由运营模式【即使没改主产品，比对时会把原单主产品补到入参主产品】
        ProductDelegate productDelegate = orderModel.getProductDelegate();
        if(null != productDelegate && null != productDelegate.getMainProduct()){
            Product mainProduct = (Product) productDelegate.getMainProduct();
            LOGGER.info("当前修改请求主动变更主产品信息-无需操作剔除，mainProduct:{},", JSONUtils.beanToJSONDefault(mainProduct));
        } else {
            LOGGER.info("当前修改请求未变更主产品信息-需操作剔除，打标，产品校验时剔除");
            expressOrderContext.setNeedDeleteOperationMode(true);
        }
    }
}
