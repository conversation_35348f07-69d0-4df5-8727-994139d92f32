package cn.jdl.oms.express.contract.extension.basic;

import cn.jdl.oms.express.contract.extension.constant.ContractLengthConstants;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.basic.BasicInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.tms.TmsBasicFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.cache.BasicCache;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.spec.model.IGoods;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Warehouse;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 合同物流纯配接单基础校验
*<AUTHOR>
*@date 2023/8/9 21:45
*/
@Extension(code = ExpressOrderProduct.CODE)
public class ContractCreateBasicInfoExtension implements IBasicInfoExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContractCreateBasicInfoExtension.class);

    /**
     * 揽收网络类型
     */
    private static final String PICKUP_TRANSPORT_NET_MODE = "pickupTransportNetMode";
    /**
     * 派送网络类型
     */
    private static final String DELIVERY_TRANSPORT_NET_MODE = "deliveryTransportNetMode";
    /**
     * 营业厅编码
     */
    private static final String BUSINESS_HALL_NO = "businessHallNo";
    /**
     * 营业厅名称
     */
    private static final String BUSINESS_HALL_NAME = "businessHallName";
    /**
     * 车队ID
     */
    private static final String VEHICLE_TEAM_ID = "vehicleTeamId";

    /**
     * 基础信息校验
     */
    @Resource
    private BasicInfoFacade basicInfoFacade;

    /**
     * 基本信息校验的转换器
     */
    @Resource
    private BasicInfoFacadeTranslator basicInfoFacadeTranslator;

    /**
     * UCC配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 商家基础资料查询
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    @Resource
    private TmsBasicFacade tmsBasicFacade;

    /**
     * 订单标识的拒收类型
     */
    private static final String REJECTION_TYPE = "rejectionType";

    /**
     * 2代表包裹维度拒收，也就是全收半退
     */
    private static final String REJECTION_TYPE_VALUE = "2";

    /**
     * 商家逆向退货场景指定退货地址必传类型
     */
    private static final String RETURN_TYPE = "11";

    @Resource
    private BasicCache basicCache;

    /**
     * 合同物流订单接单基础信息校验扩展点
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("合同物流订单接单基本信息校验扩展点执行开始");
            this.basicInfoValid(context);
            LOGGER.info("合同物流订单接单基本信息校验扩展点执行结束");
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("合同物流订单接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("合同物流订单接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 合同物流扩展点基本信息校验
     */
    private void basicInfoValid(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        // 业务身份校验
        this.businessUnitValid(orderModel); 
        // 订单类型校验
        this.orderTypeValid(orderModel);
        // 交易客户信息校验
        this.customerValid(orderModel);
        // 渠道信息校验
        this.channelValid(orderModel);
        // 产品类型校验
        this.productValid(orderModel);
        // 发货人信息校验
        this.consignorValid(orderModel);
        // 收货人信息校验
        this.consigneeValid(orderModel);
        // 财务信息校验
        this.financeValid(context);
        // 货品信息校验
        this.cargosValid(orderModel, orderModel.getCargoDelegate().getCargoList());
        // 商品信息校验
        this.goodsValid(orderModel);
        // 配送信息校验
        this.shipmentValid(orderModel);
        // 下单人唯一标识校验
        this.operatorValid(orderModel); 
        // 备注校验
        this.remarkValid(orderModel);
        // 扩展字段校验
        this.extendPropsValid(orderModel); 
        // 车型校验
        this.vehicleTypeValid(orderModel);
        // 退货信息校验
        this.returnInfoValid(orderModel);
    }

    /**
     * 业务身份校验
     */
    private void businessUnitValid(ExpressOrderModel orderModel) {
        OrderBusinessIdentity orderBusinessIdentity = orderModel.getOrderBusinessIdentity();
        if (orderBusinessIdentity == null) {
            LOGGER.error("合同物流订单接单的基本信息-业务身份信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-业务身份信息为空");
        }
        String businessUnit = orderBusinessIdentity.getBusinessUnit();
        if (StringUtils.isBlank(businessUnit)) {
            LOGGER.error("合同物流订单接单的基本信息-业务身份为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-业务身份为空");
        }
        if (!BusinessUnitEnum.CN_JDL_CONTRACT.getCode().equals(businessUnit)) {
            LOGGER.error("合同物流订单接单的基本信息-业务身份必须是合同物流");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-业务身份必须是合同物流");
        }
    }

    /**
     * 订单类型校验
     */
    private void orderTypeValid(ExpressOrderModel orderModel) {
        OrderTypeEnum orderType = orderModel.getOrderType();
        // 必填
        if (orderType == null) {
            LOGGER.error("合同物流订单接单的基本信息-订单类型为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-订单类型为空");
        }
        // 限定类型
        if (!OrderTypeEnum.DELIVERY.getCode().equals(orderType.getCode())
                && !OrderTypeEnum.RETURN_ORDER.getCode().equals(orderType.getCode())) {
            LOGGER.error("合同物流订单接单的基本信息-订单类型必须是合同物流正向或者合同物流逆向");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-订单类型必须是合同物流正向或者合同物流逆向");
        }
    }

    /**
     * 交易客户信息校验
     */
    private void customerValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 交易客户信息：必填
        Customer customer = orderModel.getCustomer();
        if (customer == null) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-交易客户信息为空");
        }

        // 青龙业主号：必填，长度限制
        String accountNo = customer.getAccountNo();
        if (StringUtils.isBlank(accountNo)) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的青龙业主号为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-交易客户信息的青龙业主号为空");
        }
        if (accountNo.length() > ContractLengthConstants.ACCOUNT_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的青龙业主号长度大于{}", ContractLengthConstants.ACCOUNT_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-交易客户信息的青龙业主号长度大于%d", ContractLengthConstants.ACCOUNT_NO_LENGTH));
        }

        // 事业部编号：必填，长度限制
        String accountNo2 = customer.getAccountNo2();
        if (StringUtils.isBlank(accountNo2)) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的事业部编号为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-交易客户信息的事业部编号为空");
        }
        if (accountNo2.length() > ContractLengthConstants.ACCOUNT_NO_2_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的事业部编号长度大于{}", ContractLengthConstants.ACCOUNT_NO_2_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-交易客户信息的事业部编号长度大于%d", ContractLengthConstants.ACCOUNT_NO_2_LENGTH));
        }

        // ECP编码：非必填，长度限制
        String accountNo3 = customer.getAccountNo3();
        if (StringUtils.isNotBlank(accountNo3) && accountNo3.length() > ContractLengthConstants.ACCOUNT_NO_3_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的ECP编码长度大于{}", ContractLengthConstants.ACCOUNT_NO_3_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-交易客户信息的ECP编码长度大于%d", ContractLengthConstants.ACCOUNT_NO_3_LENGTH));
        }

        // ECP名称：非必填，长度限制
        String accountName3 = customer.getAccountName3();
        if (StringUtils.isNotBlank(accountName3) && accountName3.length() > ContractLengthConstants.ACCOUNT_NAME_3_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-交易客户信息的ECP名称长度大于{}", ContractLengthConstants.ACCOUNT_NAME_3_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-交易客户信息的ECP名称长度大于%d", ContractLengthConstants.ACCOUNT_NAME_3_LENGTH));
        }
    }

    /**
     * 渠道信息校验
     */
    private void channelValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 渠道信息：必填
        Channel channel = orderModel.getChannel();
        if (channel == null) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-渠道信息为空");
        }

        // 商家订单号：非必填，长度限制
        String customerOrderNo = channel.getCustomerOrderNo();
        if (StringUtils.isBlank(customerOrderNo)) {
            LOGGER.info("合同物流customerOrderNo商家订单号为空，后续使用orderNo订单号补全customerOrderNo商家订单号");
        }
        if (StringUtils.isNotBlank(customerOrderNo) && customerOrderNo.length() > ContractLengthConstants.CUSTOMER_ORDER_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的商家订单号长度大于{}", ContractLengthConstants.CUSTOMER_ORDER_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-渠道信息的商家订单号长度大于%d", ContractLengthConstants.CUSTOMER_ORDER_NO_LENGTH));
        }

        // 渠道单号：非必填，长度限制
        String channelOrderNo = channel.getChannelOrderNo();
        if (StringUtils.isNotBlank(channelOrderNo) && channelOrderNo.length() > ContractLengthConstants.CHANNEL_ORDER_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道单号长度大于{}", ContractLengthConstants.CHANNEL_ORDER_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-渠道信息的渠道单号长度大于%d", ContractLengthConstants.CHANNEL_ORDER_NO_LENGTH));
        }

        // 渠道调用方来源：必填
        SystemCallerEnum systemCaller = channel.getSystemCaller();
        if (systemCaller == null) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道调用方来源为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-渠道信息的渠道调用方来源为空");
        }

        // 渠道调用方子来源：非必填，长度限制
        String systemSubCaller = channel.getSystemSubCaller();
        if (StringUtils.isNotBlank(systemSubCaller) && systemSubCaller.length() > ContractLengthConstants.SYSTEM_SUB_CALLER_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道调用方子来源长度大于{}", ContractLengthConstants.SYSTEM_SUB_CALLER_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("渠道信息的渠道调用方子来源长度大于%d", ContractLengthConstants.SYSTEM_SUB_CALLER_LENGTH));
        }

        // 渠道编号：必填，长度限制，有效性
        String channelNo = channel.getChannelNo();
        if (StringUtils.isBlank(channelNo)) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-渠道信息的渠道编码为空");
        }
        if (channelNo.length() > ContractLengthConstants.CHANNEL_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道编码长度大于{}", ContractLengthConstants.CHANNEL_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-渠道信息的渠道编码长度大于%d", ContractLengthConstants.CHANNEL_NO_LENGTH));
        }

        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.BASIC_CACHE_SWITCH)) {
            if (!basicCache.validChannelNo(orderModel.getChannel().getChannelNo())) {
                LOGGER.error("校验渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("校验渠道编码不在配置范围内，校验失败");
            }
        } else {
            // 调青龙接口进行校验
            BasicInfoFacadeRequest basicInfoFacadeRequest = basicInfoFacadeTranslator.toBasicInfoFacadeRequest(orderModel);
            BasicInfoFacadeResponse basicInfoFacadeResponse = basicInfoFacade.getBasicData(basicInfoFacadeRequest);
            boolean judgeChannelNo = false;
            for (String thisRoundChannelNo : basicInfoFacadeResponse.getBaseDataList()) {
                if (thisRoundChannelNo.equals(orderModel.getChannel().getChannelNo())) {
                    judgeChannelNo = true;
                    break;
                }
            }
            if (!judgeChannelNo) {
                LOGGER.error("合同物流订单接单的基本信息-渠道信息的渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-渠道信息的渠道编码不在配置范围内，校验失败");
            }
        }


    }

    /**
     * 产品服务信息校验
     */
    private void productValid(ExpressOrderModel model) throws DomainAbilityException {
        if (null == model.getProductDelegate()
                || CollectionUtils.isEmpty(model.getProductDelegate().getProducts())) {
            return;
        }

        List<? extends IProduct> products = model.getProductDelegate().getProducts();
        products.forEach(productObject -> {
            if (productObject == null) {
                LOGGER.error("产品服务信息为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品服务信息为空");
            }
            if (!(productObject instanceof Product)) {
                LOGGER.error("合同物流订单接单的基本信息-产品服务信息类型错误");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-产品服务信息类型错误");
            }
            Product product = (Product) productObject;

            // 产品编码：必填，长度限制
            String productNo = product.getProductNo();
            if (StringUtils.isBlank(product.getProductNo())) {
                LOGGER.error("合同物流订单接单的基本信息-产品服务信息的产品编码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-产品服务信息的产品编码为空");
            }
            if (productNo.length() > ContractLengthConstants.PRODUCT_NO_LENGTH) {
                LOGGER.error("合同物流订单接单的基本信息-产品服务信息的产品编码长度大于{}", ContractLengthConstants.PRODUCT_NO_LENGTH);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(String.format("合同物流订单接单的基本信息-产品服务信息的产品编码长度大于%d", ContractLengthConstants.PRODUCT_NO_LENGTH));
            }

            // 产品类型：必填
            if (product.getProductType() == null) {
                LOGGER.error("合同物流订单接单的基本信息-产品服务信息的产品类型为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-产品服务信息的产品类型为空");
            }
        });
    }

    /**
     * 发货人信息校验
     */
    private void consignorValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 发货人：必填
        Consignor consignor = orderModel.getConsignor();
        if (consignor == null) {
            LOGGER.error("合同物流订单接单的基本信息-发货人信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-发货人信息为空");
        }

        // 发货人姓名：必填，长度限制
        String consignorName = consignor.getConsignorName();
        if (StringUtils.isBlank(consignorName)) {
            LOGGER.error("合同物流订单接单的基本信息-发货人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-发货人姓名为空");
        }
        if (consignorName.length() > ContractLengthConstants.CONSIGNOR_NAME_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-发货人姓名长度大于{}", ContractLengthConstants.CONSIGNOR_NAME_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-发货人姓名长度大于%d", ContractLengthConstants.CONSIGNOR_NAME_LENGTH));
        }

        // 发货人手机、发货人电话：二选一必填，长度校验
        String consignorMobile = consignor.getConsignorMobile();
        String consignorPhone = consignor.getConsignorPhone();
        if (StringUtils.isBlank(consignorMobile) && StringUtils.isBlank(consignorPhone)) {
            LOGGER.error("合同物流订单接单的基本信息-发货人手机和发货人电话同时为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-发货人手机和发货人电话同时为空");
        }
        if (StringUtils.isNotBlank(consignorMobile) && consignorMobile.length() > ContractLengthConstants.CONSIGNOR_MOBILE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-发货人手机长度大于{}", ContractLengthConstants.CONSIGNOR_MOBILE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-发货人手机长度大于%d", ContractLengthConstants.CONSIGNOR_MOBILE_LENGTH));
        }
        if (StringUtils.isNotBlank(consignorPhone) && consignorPhone.length() > ContractLengthConstants.CONSIGNOR_PHONE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-发货人电话长度大于{}", ContractLengthConstants.CONSIGNOR_PHONE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-发货人电话长度大于%d", ContractLengthConstants.CONSIGNOR_PHONE_LENGTH));
        }

        // 发货人公司：非必填，长度校验
        String consignorCompany = consignor.getConsignorCompany();
        if (StringUtils.isNotBlank(consignorCompany) && consignorCompany.length() > ContractLengthConstants.CONSIGNOR_COMPANY_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-发货人公司长度大于{}", ContractLengthConstants.CONSIGNOR_COMPANY_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-发货人公司长度大于%d", ContractLengthConstants.CONSIGNOR_COMPANY_LENGTH));
        }

        // 发货人证件类型、发货人证件号码：非必填，按发货人证件类型是否有值校验
        IdentityTypeEnum consignorIdType = consignor.getConsignorIdType();
        String consignorIdNo = consignor.getConsignorIdNo();
        // 证件类型有值，校验发货人证件号码
        if (consignorIdType != null) {
            // 必填
            if (StringUtils.isBlank(consignorIdNo)) {
                LOGGER.error("合同物流订单接单的基本信息-发货人证件号码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-发货人证件号码为空");
            }
            // 长度校验
            if (consignorIdNo.length() < ContractLengthConstants.CONSIGNOR_ID_NO_LENGTH_MIN
                    || consignorIdNo.length() > ContractLengthConstants.CONSIGNOR_ID_NO_LENGTH_MAX) {
                LOGGER.error("接单基本信息校验活动能力开始执行,证件号不能小于6位或者大于20位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("证件号不能小于6位或者大于20位");
            }
            // 证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
            if ((consignorIdType == IdentityTypeEnum.ID_CARD || consignorIdType == IdentityTypeEnum.HOUSEHOLD_REGISTER)
                    && (!ContractLengthConstants.ID_CARD_LENGTH.equals(consignorIdNo.length()) && !ContractLengthConstants.HOUSEHOLD_REGISTER_LENGTH.equals(consignorIdNo.length()))) {
                LOGGER.error("发货人的身份证号码不等于15位或者18位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("发货人的身份证号码不等于15位或者18位");
            }
        }

        // 发货人地址信息校验
        addressValid(consignor.getAddress(), InitiatorTypeEnum.CONSIGNOR.getDesc());

        // 发货仓库校验
        warehouseValid(consignor.getCustomerWarehouse());
    }

    /**
     * 收货人信息校验
     */
    private void consigneeValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 收货人：必填
        Consignee consignee = orderModel.getConsignee();
        if (consignee == null) {
            LOGGER.error("合同物流订单接单的基本信息-收货人为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-收货人为空");
        }

        // 收货人姓名：必填，长度限制
        String consigneeName = consignee.getConsigneeName();
        if (StringUtils.isBlank(consigneeName)) {
            LOGGER.error("合同物流订单接单的基本信息-收货人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-收货人姓名为空");
        }
        if (consigneeName.length() > ContractLengthConstants.CONSIGNEE_NAME_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-收货人姓名长度大于{}", ContractLengthConstants.CONSIGNEE_NAME_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-收货人姓名长度大于%d", ContractLengthConstants.CONSIGNEE_NAME_LENGTH));
        }

        // 收货人手机、收货人电话：非京东渠道二选一必填，长度校验
        String consigneeMobile = consignee.getConsigneeMobile();
        String consigneePhone = consignee.getConsigneePhone();
        if (!BusinessConstants.JD_CHANNEL_NO.equals(orderModel.getChannel().getChannelNo())
                && StringUtils.isBlank(consigneeMobile) && StringUtils.isBlank(consigneePhone)) {
            LOGGER.error("合同物流订单接单的基本信息-收货人手机和收货人电话同时为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-收货人手机和收货人电话同时为空");
        }
        if (StringUtils.isNotBlank(consigneeMobile) && consigneeMobile.length() > ContractLengthConstants.CONSIGNEE_MOBILE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-收货人手机长度大于{}", ContractLengthConstants.CONSIGNEE_MOBILE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-收货人手机长度大于%d", ContractLengthConstants.CONSIGNEE_MOBILE_LENGTH));
        }
        if (StringUtils.isNotBlank(consigneePhone) && consigneePhone.length() > ContractLengthConstants.CONSIGNEE_PHONE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-收货人电话长度大于{}", ContractLengthConstants.CONSIGNEE_PHONE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-收货人电话长度大于%d", ContractLengthConstants.CONSIGNEE_PHONE_LENGTH));
        }

        // 收货人公司：非必填，长度校验
        String consigneeCompany = consignee.getConsigneeCompany();
        if (StringUtils.isNotBlank(consigneeCompany) && consigneeCompany.length() > ContractLengthConstants.CONSIGNEE_COMPANY_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-发货人公司长度大于{}", ContractLengthConstants.CONSIGNOR_COMPANY_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-发货人公司长度大于%d", ContractLengthConstants.CONSIGNOR_COMPANY_LENGTH));
        }

        // 收货人证件类型、收货人证件号码：非必填，按收货人证件类型是否有值校验
        IdentityTypeEnum consigneeIdType = consignee.getConsigneeIdType();
        String consigneeIdNo = consignee.getConsigneeIdNo();
        // 证件类型有值，校验收货人证件号码
        if (consigneeIdType != null) {
            // 必填
            if (StringUtils.isBlank(consigneeIdNo)) {
                LOGGER.error("合同物流订单接单的基本信息-收货人证件号码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-收货人证件号码为空");
            }
            // 长度校验
            if (consigneeIdNo.length() < ContractLengthConstants.CONSIGNEE_ID_NO_LENGTH_MIN
                    || consigneeIdNo.length() > ContractLengthConstants.CONSIGNEE_ID_NO_LENGTH_MAX) {
                LOGGER.error("接单基本信息校验活动能力开始执行,证件号不能小于6位或者大于20位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("证件号不能小于6位或者大于20位");
            }
            // 证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
            if ((consigneeIdType == IdentityTypeEnum.ID_CARD || consigneeIdType == IdentityTypeEnum.HOUSEHOLD_REGISTER)
                    && (!ContractLengthConstants.ID_CARD_LENGTH.equals(consigneeIdNo.length()) && !ContractLengthConstants.HOUSEHOLD_REGISTER_LENGTH.equals(consigneeIdNo.length()))) {
                LOGGER.error("收货人的身份证号码不等于15位或者18位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("收货人的身份证号码不等于15位或者18位");
            }
        }

        // 收货人地址信息校验
        addressValid(consignee.getAddress(), InitiatorTypeEnum.CONSIGNEE.getDesc());
    }

    /**
     * 地址信息校验
     */
    private void addressValid(Address address, String contact) throws DomainAbilityException {
        if (address == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}地址信息为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s地址信息为空", contact));
        }
        if (StringUtils.isBlank(address.getAddress())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}详细地址为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s详细地址为空", contact));
        }
        if (address.getAddress().length() > ContractLengthConstants.ADDRESS_LENGTH) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}详细地址长度不能超过200", contact);
            // 此处开关默认false不校验，无印港澳放开到350，若无问题下线开关直接删除校验逻辑即可
            BatrixSwitch.applyDefNotExecute(BatrixSwitchKey.ADDRESS_LENGTH_VALIDATION, (bTrue) -> {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(contact + "详细地址长度不能超过200");
            });
        }
        if (address.getFenceTrusted() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}地址是否围栏信任为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s地址是否围栏信任为空", contact));
        }
    }

    /**
     * 仓库校验
     */
    private void warehouseValid(Warehouse warehouse) {
        // 仓库信息非必填
        if (warehouse == null) {
            return;
        }
        // 仓库编码：非必填，长度校验
        String warehouseNo = warehouse.getWarehouseNo();
        if (StringUtils.isNotBlank(warehouseNo) && warehouseNo.length() > ContractLengthConstants.WAREHOUSE_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-仓库编码长度大于{}", ContractLengthConstants.WAREHOUSE_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-仓库编码长度大于%d", ContractLengthConstants.WAREHOUSE_NO_LENGTH));
        }
        // 仓库类型：非必填，长度校验
        String warehouseSource = warehouse.getWarehouseSource();
        if (StringUtils.isNotBlank(warehouseSource) && warehouseSource.length() > ContractLengthConstants.WAREHOUSE_SOURCE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-仓库类型长度大于{}", ContractLengthConstants.WAREHOUSE_SOURCE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-仓库类型长度大于%d", ContractLengthConstants.WAREHOUSE_SOURCE_LENGTH));
        }
        // 仓库名称：非必填，长度校验
        String warehouseName = warehouse.getWarehouseName();
        if (StringUtils.isNotBlank(warehouseName) && warehouseName.length() > ContractLengthConstants.WAREHOUSE_NAME_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-仓库名称长度大于{}", ContractLengthConstants.WAREHOUSE_NAME_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-仓库名称长度大于%d", ContractLengthConstants.WAREHOUSE_NAME_LENGTH));
        }
    }

    /**
     * 财务信息校验
     */
    private void financeValid(ExpressOrderContext expressOrderContext) throws DomainAbilityException {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();

        // 财务信息：必填
        Finance finance = orderModel.getFinance();
        if (finance == null) {
            LOGGER.error("合同物流订单接单的基本信息-财务信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-财务信息为空");
        }

        // 预估费用：非必填，合法性校验
        Money estimateAmount = finance.getEstimateAmount();
        if (estimateAmount != null && new BigDecimal(ContractLengthConstants.ESTIMATE_AMOUNT_MAX).compareTo(estimateAmount.getAmount()) <= 0) {
            LOGGER.error("当前的运费的值为: {}, 预估运费大于等于{}.", orderModel.getFinance().getEstimateAmount().getAmount(), ContractLengthConstants.ESTIMATE_AMOUNT_MAX);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("预估运费大于等于一百万");
        }

        // 支付环节：必填
        PaymentStageEnum paymentStage = finance.getPaymentStage();
        if (paymentStage == null) {
            LOGGER.error("合同物流订单接单的基本信息-财务信息的支付环节为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-财务信息的支付环节为空");
        }

        // 结算方式：必填
        SettlementTypeEnum settlementType = finance.getSettlementType();
        this.settlementTypeValid(settlementType, orderModel);

        // 结算账号：结算方式为寄付月结必填，长度校验；非月结必须为空
        String settlementAccountNo = finance.getSettlementAccountNo();
        if (SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(settlementType.getCode())) {
            if (StringUtils.isBlank(settlementAccountNo)) {
                LOGGER.error("合同物流订单接单的基本信息-财务信息的结算账号为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-财务信息的结算账号为空");
            }
            if (settlementAccountNo.length() > ContractLengthConstants.SETTLEMENT_ACCOUNT_NO_LENGTH) {
                LOGGER.error("合同物流订单接单的基本信息-结算账号长度大于{}", ContractLengthConstants.SETTLEMENT_ACCOUNT_NO_LENGTH);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(String.format("合同物流订单接单的基本信息-结算账号长度大于%d", ContractLengthConstants.SETTLEMENT_ACCOUNT_NO_LENGTH));
            }
        } else {
            if (StringUtils.isNotBlank(settlementAccountNo)) {
                LOGGER.error("合同物流订单接单的基本信息-结算方式非月结结算账号必须为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-结算方式非月结结算账号必须为空");
            }
        }
    }

    /**
     * 结算方式校验
     */
    private void settlementTypeValid(SettlementTypeEnum settlementType, ExpressOrderModel orderModel) {

        // 必填
        if (settlementType == null) {
            LOGGER.error("合同物流订单接单的基本信息-财务信息的结算方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-财务信息的结算方式为空");
        }

        //合同物流正向单结算方式只能为寄付月结或到付现结
        if (OrderTypeEnum.DELIVERY.getCode().equals(orderModel.getOrderType().getCode())) {
            if (!SettlementTypeEnum.MONTHLY_PAYMENT.getCode().equals(settlementType.getCode())
                    && !SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(settlementType.getCode())) {
                LOGGER.error("合同物流订单接单的基本信息-结算方式只能为寄付月结或到付现结");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-结算方式只能为寄付月结或到付现结");
            }
        }

    }

    /**
     * 货品信息校验
     */
    private void cargosValid(ExpressOrderModel orderModel, List<? extends ICargo> cargos) throws DomainAbilityException {
        if (CollectionUtils.isNotEmpty(cargos)) {
            cargos.forEach(cargoObject -> {
                if (cargoObject == null) {
                    LOGGER.error("合同物流订单接单的基本信息-货品信息为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("合同物流订单接单的基本信息-货品信息为空");
                }
                if (!(cargoObject instanceof Cargo)) {
                    LOGGER.error("合同物流订单接单的基本信息-货品信息类型错误");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("合同物流订单接单的基本信息-货品信息类型错误");
                }
                Cargo cargo = (Cargo) cargoObject;

                // 货品名称：必填，长度校验
                String cargoName = cargo.getCargoName();
                if (StringUtils.isBlank(cargoName)) {
                    LOGGER.error("合同物流订单接单的基本信息-货品名称为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("合同物流订单接单的基本信息-货品名称为空");
                }
                if (cargoName.length() > ContractLengthConstants.CARGO_NAME_LENGTH) {
                    LOGGER.error("合同物流订单接单的基本信息-货品名称长度大于{}", ContractLengthConstants.CARGO_NAME_LENGTH);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(String.format("合同物流订单接单的基本信息-货品名称长度大于%d", ContractLengthConstants.CARGO_NAME_LENGTH));
                }

                // 货品编码：非必填，长度校验
                String cargoNo = cargo.getCargoNo();
                if (StringUtils.isNotBlank(cargoNo) && cargoNo.length() > ContractLengthConstants.CARGO_NO_LENGTH) {
                    LOGGER.error("合同物流订单接单的基本信息-货品编码长度大于{}", ContractLengthConstants.CARGO_NO_LENGTH);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(String.format("合同物流订单接单的基本信息-货品编码长度大于%d", ContractLengthConstants.CARGO_NO_LENGTH));
                }

                // 货品类型：非必填，长度校验
                String cargoType = cargo.getCargoType();
                if (StringUtils.isNotBlank(cargoType) && cargoType.length() > ContractLengthConstants.CARGO_TYPE_LENGTH) {
                    LOGGER.error("合同物流订单接单的基本信息-货品类型长度大于{}", ContractLengthConstants.CARGO_TYPE_LENGTH);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(String.format("合同物流订单接单的基本信息-货品类型长度大于%d", ContractLengthConstants.CARGO_TYPE_LENGTH));
                }

                // 货品体积：必填，合法性校验
                Volume cargoVolume = cargo.getCargoVolume();
                this.cargoVolumeValid(orderModel, cargoVolume);

                // 货品重量：必填，合法性校验
                Weight cargoWeight = cargo.getCargoWeight();
                this.cargoWeightValid(orderModel, cargoWeight);

                // 货品数量：必填
                Quantity cargoQuantity = cargo.getCargoQuantity();
                this.cargoQuantityValid(orderModel, cargoQuantity);

                // 货品备注：非必填，长度校验
                String cargoRemark = cargo.getCargoRemark();
                if (StringUtils.isNotBlank(cargoRemark) && cargoRemark.length() > ContractLengthConstants.CARGO_REMARK_LENGTH) {
                    LOGGER.error("合同物流订单接单的基本信息-货品备注长度大于{}", ContractLengthConstants.CARGO_REMARK_LENGTH);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(String.format("合同物流订单接单的基本信息-货品备注长度大于%d", ContractLengthConstants.CARGO_REMARK_LENGTH));
                }
            });
        }
    }

    /**
     * 货品体积校验
     */
    private void cargoVolumeValid(ExpressOrderModel orderModel, Volume cargoVolume) {
        if (cargoVolume == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品体积信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品体积信息为空");
        }
        if (cargoVolume.getUnit() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品体积单位为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品体积单位为空");
        }
        if (cargoVolume.getValue() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品体积的值为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品体积的值为空");
        }
        BigDecimal volumeValue = this.getVolumeValue(cargoVolume);
        if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderModel.getOrderType().getCode())
                && isHalfReceive(orderModel)
                && volumeValue.compareTo(BigDecimal.ZERO) == 0) {
            // 逆向单且全收半退允许为零
            return;
        }
        // systemCaller=SupplyOFC，货品数量体积重量支持0
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel)
                && volumeValue.compareTo(BigDecimal.ZERO) == 0) {
            LOGGER.info("systemCaller=SupplyOFC，货品体积支持0");
            return;
        }
        String minValue = expressUccConfigCenter.getContractCargoVolumeMin();
        String maxValue = expressUccConfigCenter.getContractCargoVolumeMax();
        if (volumeValue.compareTo(new BigDecimal(minValue)) < 0) {
            LOGGER.error("合同物流订单接单的基本信息-货品体积必须大于{}立方厘米", minValue);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-货品体积必须大于%s立方厘米", minValue));
        }
        if (volumeValue.compareTo(new BigDecimal(maxValue)) >= 0) {
            LOGGER.error("合同物流订单接单的基本信息-货品体积必须小于{}立方厘米", maxValue);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-货品体积必须小于%s立方厘米", maxValue));
        }
    }

    /**
     * 体积单位值转换
     */
    private BigDecimal getVolumeValue(Volume volume) {
        //默认单位是立方厘米，如果不是则转成立方厘米
        if (VolumeTypeEnum.DM3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000"));
        }
        if (VolumeTypeEnum.M3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000000"));
        }
        return volume.getValue();
    }

    /**
     * 货品重量校验
     */
    private void cargoWeightValid(ExpressOrderModel orderModel, Weight cargoWeight) {
        if (cargoWeight == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品重量信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品重量信息为空");
        }
        if (cargoWeight.getUnit() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品重量单位为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品重量单位为空");
        }
        if (cargoWeight.getValue() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品重量的值为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品重量的值为空");
        }
        BigDecimal value = cargoWeight.getValue();
        if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderModel.getOrderType().getCode())
                && isHalfReceive(orderModel)
                && value.compareTo(BigDecimal.ZERO) == 0) {
            // 逆向单且全收半退允许为零
            return;
        }
        // systemCaller=SupplyOFC，货品数量体积重量支持0
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel)
                && value.compareTo(BigDecimal.ZERO) == 0) {
            LOGGER.info("systemCaller=SupplyOFC，货品重量支持0");
            return;
        }
        String minValue = expressUccConfigCenter.getContractCargoWeightMin();
        String maxValue = expressUccConfigCenter.getContractCargoWeightMax();
        if (value.compareTo(new BigDecimal(minValue)) < 0) {
            LOGGER.error("合同物流订单接单的基本信息-货品重量必须大于{}千克", minValue);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-货品重量必须大于%s千克", minValue));
        }
        if (value.compareTo(new BigDecimal(maxValue)) >= 0) {
            LOGGER.error("合同物流订单接单的基本信息-货品重量必须小于{}千克", maxValue);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-货品重量必须小于%s千克", maxValue));
        }
    }

    /**
     * 货品数量必填值校验
     */
    private void cargoQuantityValid(ExpressOrderModel orderModel, Quantity cargoQuantity) {
        if (cargoQuantity == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品数量为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品数量为空");
        }
        if (cargoQuantity.getValue() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品数量的值为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品数量的值为空");
        }
        if (cargoQuantity.getUnit() == null) {
            LOGGER.error("合同物流订单接单的基本信息-货品数量的单位为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-货品数量的单位为空");
        }
        // systemCaller=SupplyOFC，货品数量体积重量支持0
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel)
                && cargoQuantity.getValue().intValue() == 0) {
            LOGGER.info("systemCaller=SupplyOFC，货品数量支持0");
            return;
        }
        Integer contractCargoNumMin = expressUccConfigCenter.getContractCargoNumMin();
        Integer contractCargoNumMax = expressUccConfigCenter.getContractCargoNumMax();
        //货品的数量不能小于0或者大于50000,可配置
        if (cargoQuantity.getValue().intValue() < contractCargoNumMin || cargoQuantity.getValue().intValue() > contractCargoNumMax) {
            LOGGER.error("合同物流订单接单的基本信息，货品数量小于" + contractCargoNumMin + "或大于" + contractCargoNumMax);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("货品数量小于" + contractCargoNumMin + "或大于" + contractCargoNumMax);
        }
    }

    /**
     * 商品信息校验
     */
    private void goodsValid(ExpressOrderModel orderModel) {
        if (CollectionUtils.isEmpty(orderModel.getGoodsDelegate().getGoodsList())) {
            return;
        }
        List<? extends IGoods> goodsObjectList = orderModel.getGoodsDelegate().getGoodsList();
        goodsObjectList.forEach(goodsObject -> {
            if (goodsObject == null) {
                return;
            }
            if (!(goodsObject instanceof Goods)) {
                LOGGER.error("合同物流订单接单的基本信息-商品信息类型错误");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品信息类型错误");
            }
            Goods goods = (Goods) goodsObject;

            // 商品名称：必填，长度校验
            String goodsName = goods.getGoodsName();
            if (StringUtils.isBlank(goodsName)) {
                LOGGER.error("合同物流订单接单的基本信息-商品名称为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品名称为空");
            }
            if (goodsName.length() > ContractLengthConstants.GOODS_NAME_LENGTH) {
                LOGGER.error("合同物流订单接单的基本信息-商品名称长度大于{}", ContractLengthConstants.GOODS_NAME_LENGTH);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(String.format("合同物流订单接单的基本信息-商品名称长度大于%d", ContractLengthConstants.GOODS_NAME_LENGTH));
            }

            // 商品编码：必填，长度校验
            String goodsNo = goods.getGoodsNo();
            if (StringUtils.isBlank(goodsNo)) {
                LOGGER.error("合同物流订单接单的基本信息-商品编码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品编码为空");
            }
            if (goodsNo.length() > ContractLengthConstants.GOODS_NO_LENGTH) {
                LOGGER.error("合同物流订单接单的基本信息-商品编码长度大于{}", ContractLengthConstants.GOODS_NO_LENGTH);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(String.format("合同物流订单接单的基本信息-商品编码长度大于%d", ContractLengthConstants.GOODS_NO_LENGTH));
            }

            // 商品唯一编号：必填，长度校验
            String goodsUniqueCode = goods.getGoodsUniqueCode();
            if (StringUtils.isBlank(goodsUniqueCode)) {
                LOGGER.error("合同物流订单接单的基本信息-商品唯一编号为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品唯一编号为空");
            }
            if (goodsUniqueCode.length() > ContractLengthConstants.GOODS_UNIQUE_CODE_LENGTH) {
                LOGGER.error("合同物流订单接单的基本信息-商品唯一编号长度大于{}", ContractLengthConstants.GOODS_UNIQUE_CODE_LENGTH);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(String.format("合同物流订单接单的基本信息-商品唯一编号长度大于%d", ContractLengthConstants.GOODS_UNIQUE_CODE_LENGTH));
            }

            // 商品数量：必填
            Quantity quantity = goods.getGoodsQuantity();
            if (quantity == null) {
                LOGGER.error("合同物流订单接单的基本信息-商品数量信息为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品数量信息为空");
            }
            if (quantity.getValue() == null) {
                LOGGER.error("合同物流订单接单的基本信息-商品数量值为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-商品数量值为空");
            }
        });
    }

    /**
     * 配送信息校验
     */
    private void shipmentValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 配送信息：必填
        Shipment shipment = orderModel.getShipment();
        if (shipment == null) {
            LOGGER.error("合同物流订单接单的基本信息-配送信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-配送信息为空");
        }

        // 揽收方式：正向必填
        PickupTypeEnum pickupType = shipment.getPickupType();
        if (OrderTypeEnum.DELIVERY.getCode().equals(orderModel.getOrderType().getCode())) {
            if (pickupType == null) {
                LOGGER.error("合同物流订单接单的基本信息-揽收方式为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-揽收方式为空");
            }
        }

        // 派送方式：必填
        DeliveryTypeEnum deliveryType = shipment.getDeliveryType();
        if (deliveryType == null) {
            LOGGER.error("合同物流订单接单的基本信息-派送方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-派送方式为空");
        }

        // 始发站编码：非必填，长度校验
        String startStationNo = shipment.getStartStationNo();
        if (StringUtils.isNotBlank(startStationNo) && startStationNo.length() > ContractLengthConstants.START_STATION_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-始发站编码长度大于{}", ContractLengthConstants.START_STATION_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-始发站编码长度大于%d", ContractLengthConstants.START_STATION_NO_LENGTH));
        }

        // 期望取件开始时间：揽收方式为上门取件必填
        Date expectPickupStartTime = shipment.getExpectPickupStartTime();
        /*if (pickupType != null && PickupTypeEnum.ON_SITE_PICK.getCode().equals(pickupType.getCode()) && expectPickupStartTime == null) {
            LOGGER.error("合同物流订单接单的基本信息-期望取件开始时间为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-期望取件开始时间为空");
        }*/

        // 期望取件结束时间：揽收方式为上门取件必填
        Date expectPickupEndTime = shipment.getExpectPickupEndTime();
        /*if (pickupType != null && PickupTypeEnum.ON_SITE_PICK.getCode().equals(pickupType.getCode()) && expectPickupEndTime == null) {
            LOGGER.error("合同物流订单接单的基本信息-期望取件结束时间为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-期望取件结束时间为空");
        }*/

        // 期望取件开始时间不能早于当前时间
        if (expectPickupStartTime != null) {
            Date now = new Date();
            if (expectPickupStartTime.before(now)) {
                LOGGER.error("合同物流订单接单的基本信息-期望取件开始时间不能早于当前时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("合同物流订单接单的基本信息-期望取件开始时间不能早于当前时间");
            }
        }

        // 期望取件开始时间不能晚于期望取件结束时间
        if (expectPickupStartTime != null && expectPickupEndTime != null
                && expectPickupStartTime.after(expectPickupEndTime)) {
            LOGGER.error("合同物流订单接单的基本信息-期望取件开始时间不能晚于期望取件结束时间");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-期望取件开始时间不能晚于期望取件结束时间");
        }

        // 期望送达开始时间、期望送达结束时间：非必填，开始时间不能晚于结束时间
        Date expectDeliveryStartTime = shipment.getExpectDeliveryStartTime();
        Date expectDeliveryEndTime = shipment.getExpectDeliveryEndTime();
        if (expectDeliveryStartTime != null && expectDeliveryEndTime != null
                && expectDeliveryStartTime.after(expectDeliveryEndTime)) {
            LOGGER.error("合同物流订单接单的基本信息-期望送达开始时间不能晚于期望送达结束时间");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-期望送达开始时间不能晚于期望送达结束时间");
        }

        // 扩展字段校验
        shipmentExtendPropsValid(shipment);
    }

    /**
     * 配送信息扩展字段校验
     */
    private void shipmentExtendPropsValid(Shipment shipment) {
        if (shipment == null || shipment.getExtendProps() == null) {
            return;
        }
        Map<String, String> extendProps = shipment.getExtendProps();
        if (MapUtils.isEmpty(extendProps)) {
            return;
        }
        String shipmentExtendPropsJsonString = extendProps.get(AttachmentKeyEnum.SHIPMENT_EXTEND_PROPS.getKey());
        if (StringUtils.isBlank(shipmentExtendPropsJsonString)) {
            return;
        }
        Map map = JSONUtils.jsonToMap(shipmentExtendPropsJsonString);
        if (MapUtils.isEmpty(map)) {
            return;
        }
        // 揽收网络类型：非必填，长度校验
        String pickupTransportNetMode = (String) map.get(PICKUP_TRANSPORT_NET_MODE);
        if (StringUtils.isNotBlank(pickupTransportNetMode) && pickupTransportNetMode.length() > ContractLengthConstants.PICKUP_TRANSPORT_NET_MODE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-揽收网络类型长度大于{}", ContractLengthConstants.PICKUP_TRANSPORT_NET_MODE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-揽收网络类型长度大于%d", ContractLengthConstants.PICKUP_TRANSPORT_NET_MODE_LENGTH));
        }

        // 派送网络类型：非必填，长度校验
        String deliveryTransportNetMode = (String) map.get(DELIVERY_TRANSPORT_NET_MODE);
        if (StringUtils.isNotBlank(deliveryTransportNetMode) && deliveryTransportNetMode.length() > ContractLengthConstants.DELIVERY_TRANSPORT_NET_MODE_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-派送网络类型长度大于{}", ContractLengthConstants.DELIVERY_TRANSPORT_NET_MODE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-派送网络类型长度大于%d", ContractLengthConstants.DELIVERY_TRANSPORT_NET_MODE_LENGTH));
        }

        // 营业厅编码：非必填，长度校验
        String businessHallNo = (String) map.get(BUSINESS_HALL_NO);
        if (StringUtils.isNotBlank(businessHallNo) && businessHallNo.length() > ContractLengthConstants.BUSINESS_HALL_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-营业厅编码长度大于{}", ContractLengthConstants.BUSINESS_HALL_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-营业厅编码长度大于%d", ContractLengthConstants.BUSINESS_HALL_NO_LENGTH));
        }

        // 营业厅名称：非必填，长度校验
        String businessHallName = (String) map.get(BUSINESS_HALL_NAME);
        if (StringUtils.isNotBlank(businessHallName) && businessHallName.length() > ContractLengthConstants.BUSINESS_HALL_NAME_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-营业厅名称长度大于{}", ContractLengthConstants.BUSINESS_HALL_NAME_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-营业厅名称长度大于%d", ContractLengthConstants.BUSINESS_HALL_NAME_LENGTH));
        }

        // 车队ID：非必填，长度校验
        String vehicleTeamId = (String) map.get(VEHICLE_TEAM_ID);
        if (StringUtils.isNotBlank(vehicleTeamId) && vehicleTeamId.length() > ContractLengthConstants.VEHICLE_TEAM_ID_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-车队ID长度大于{}", ContractLengthConstants.VEHICLE_TEAM_ID_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-车队ID长度大于%d", ContractLengthConstants.VEHICLE_TEAM_ID_LENGTH));
        }
    }

    /**
     * 下单人唯一标识校验
     */
    private void operatorValid(ExpressOrderModel orderModel) {
        // 必填，长度校验
        String operator = orderModel.getOperator();
        if (StringUtils.isBlank(operator)) {
            LOGGER.error("合同物流订单接单的基本信息-下单人唯一标识为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("合同物流订单接单的基本信息-下单人唯一标识为空");
        }
        if (operator.length() > ContractLengthConstants.OPERATOR_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-下单人唯一标识长度大于{}", ContractLengthConstants.OPERATOR_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-下单人唯一标识长度大于%d", ContractLengthConstants.OPERATOR_LENGTH));
        }
    }

    /**
     * 备注校验
     */
    private void remarkValid(ExpressOrderModel orderModel) {
        // 非必填，长度校验
        String remark = orderModel.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > ContractLengthConstants.REMARK_LENGTH) {
            LOGGER.error("合同物流订单接单的基本信息-下单人唯一标识长度大于{}", ContractLengthConstants.REMARK_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的基本信息-下单人唯一标识长度大于%d", ContractLengthConstants.REMARK_LENGTH));
        }
    }

    /**
     * 判断是否全收半退
     */
    private boolean isHalfReceive(ExpressOrderModel orderModel) {
        return org.apache.commons.collections.MapUtils.isNotEmpty(orderModel.getOrderSign())
                && REJECTION_TYPE_VALUE.equals(orderModel.getOrderSign().get(REJECTION_TYPE));
    }

    /**
     * 扩展字段校验
     */
    private void extendPropsValid(ExpressOrderModel orderModel) {
        // 扩展字段：非必填
        Map<String, String> extendProps = orderModel.getExtendProps();
        if (extendProps == null || extendProps.isEmpty()) {
            return;
        }

        // 销售员（专属快递员）：非必填，长度校验
        String salesNo = extendProps.get(AttachmentKeyEnum.SALES_NO.getKey());
        if (StringUtils.isNotBlank(salesNo) && salesNo.length() > ContractLengthConstants.SALES_NO_LENGTH) {
            LOGGER.error("合同物流订单接单的扩展字段-专属快递员长度大于{}", ContractLengthConstants.SALES_NO_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的扩展字段-专属快递员长度大于%d", ContractLengthConstants.SALES_NO_LENGTH));
        }

        // 销售员来源类型（专属快递员类型）：非必填，长度校验
        String salesSourceType = extendProps.get(AttachmentKeyEnum.SALES_SOURCE_TYPE.getKey());
        if (StringUtils.isNotBlank(salesSourceType) && salesSourceType.length() > ContractLengthConstants.SALES_SOURCE_TYPE_LENGTH) {
            LOGGER.error("合同物流订单接单的扩展字段-专属快递员类型长度大于{}", ContractLengthConstants.SALES_SOURCE_TYPE_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的扩展字段-专属快递员类型长度大于%d", ContractLengthConstants.SALES_SOURCE_TYPE_LENGTH));
        }

        // 扩展信息：非必填，长度校验
        String extendInfos = extendProps.get(AttachmentKeyEnum.EXTEND_INFOS.getKey());
        if (StringUtils.isNotBlank(extendInfos) && extendInfos.length() > ContractLengthConstants.ORDER_EXTEND_INFOS_LENGTH) {
            LOGGER.error("合同物流订单接单的扩展字段-扩展信息长度大于{}", ContractLengthConstants.ORDER_EXTEND_INFOS_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单的扩展字段-扩展信息长度大于%d", ContractLengthConstants.ORDER_EXTEND_INFOS_LENGTH));
        }
    }

    /**
     * 车型校验
     *
     * @param orderModel
     */
    private void vehicleTypeValid(ExpressOrderModel orderModel){
        //车型编码非必填，为空不校验
        if(orderModel.getShipment() == null || StringUtils.isBlank(orderModel.getShipment().getVehicleType())){
            return;
        }

        //非特定产品编码，不执行校验
        if(!isNeedVehicleTypeValidProduct(orderModel)){
            LOGGER.info("合同物流订单接单-非特定产品编码不需要车型校验");
            return;
        }

        //校验车型是否存在
        String vehicleType = orderModel.getShipment().getVehicleType();
        boolean checkVehicleTypExist = tmsBasicFacade.checkVehicleTypExist(orderModel.getShipment().getVehicleType());
        if(!checkVehicleTypExist){
            LOGGER.error("合同物流订单接单-车型校验未通过vehicleType:{}",vehicleType);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("合同物流订单接单-车型校验未通过 %s",vehicleType));
        }
    }

    /**
     *
     * 根据产品编码判断是否需要执行校验车型
     *
     * 产品编码需要满足：
     * fr-m-0009（B2B整车）、
     * fr-m-0010（B2B零担）、
     * tc-m-0006（大票直送）、
     * tc-m-0007（整车专送）时，调tms-basic车型校验
     * @return
     */
    private boolean isNeedVehicleTypeValidProduct(ExpressOrderModel orderModel){
        if (null == orderModel.getProductDelegate()
                || CollectionUtils.isEmpty(orderModel.getProductDelegate().getProducts())) {
            return false;
        }

        //产品编码为空不校验
        List<String> productCodesList = orderModel.getProductDelegate().getProductCodes();
        if(CollectionUtils.isEmpty(productCodesList)){
            return false;
        }

        List<String> needVehicleTypeValidProductList = Arrays.asList(ProductEnum.CONTRACT_LD.getCode(),
                ProductEnum.CONTRACT_ZC.getCode(),
                ProductEnum.CONTRACT_DPZS.getCode(),
                ProductEnum.CONTRACT_SUPPLY.getCode());

        //存在指定的产品编码，就返回需要校验
        for (String productCode : productCodesList) {
           if(needVehicleTypeValidProductList.contains(productCode)){
               return true;
           }
        }

        return false;
    }

    /**
     * 退货信息校验
     */
    private void returnInfoValid(ExpressOrderModel orderModel){
        //商家逆向场景指定退货地址
        if (orderModel.getReturnInfoVo() != null &&
                RETURN_TYPE.equals(orderModel.getReturnInfoVo().getReturnType())) {
            Consignee consignee = orderModel.getReturnInfoVo().getConsignee();
            if (StringUtils.isBlank(consignee.getConsigneeName())
                    || (StringUtils.isBlank(consignee.getConsigneePhone())  && StringUtils.isBlank(consignee.getConsigneeMobile()))
                    || consignee.getAddress() == null
                    || StringUtils.isBlank(consignee.getAddress().getAddress())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                        .withCustom("合同物流订单接单的基本信息-指定退货地址信息不全");
            }
        }
    }
}
