package cn.jdl.oms.express.freight.extension.init;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.init.IInitExtension;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 接单初始化
 */
@Extension(code = ExpressOrderProduct.CODE)
public class FreightCreateInitExtension implements IInitExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightCreateInitExtension.class);

    /**
     * 接单初始化隐藏标
     */
    private static final String HIDDEN_MARK = "00000";

    /**
     * 初始化
     *
     * @param expressOrderContext
     * @throws AbilityExtensionException
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("快运接单信息初始化开始");
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
            //初始化已接单
            modelCreator.setOrderStatusCustom(ExpressOrderStatusCustomEnum.RECEIVED.customOrderStatus());
            expressOrderContext.getOrderModel().complement().complementCustomStatus(this, modelCreator);
            //初始化hiddenMark
            expressOrderContext.getOrderModel().setHiddenMark(this, HIDDEN_MARK);
            //打印次数
            expressOrderContext.getOrderModel().putAttachment(OrderConstants.PRINT_TIMES, OrderConstants.INIT_VALUE);
            //打印状态
            expressOrderContext.getOrderModel().putAttachment(OrderConstants.PRINT_STATUS, OrderConstants.INIT_VALUE);
            //营业厅信息：快运C2C需要补全
            if (BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(expressOrderContext.getBusinessIdentity().getBusinessUnit())) {
                expressOrderContext.putExtMaps(ContextInfoEnum.COMPLEMENT_BUSINESS_HALL_FLAG.getCode(), OrderConstants.COMPLEMENT_BUSINESS_HALL_FLAG_YES);
            }
            //揽收站点/车队校验失败拒单标示：快运需要拒单
            expressOrderContext.putExtMaps(ContextInfoEnum.CHECK_PICKUP_STATION_FLAG.getCode(), OrderConstants.CHECK_PICKUP_STATION_FLAG_YES);
            //正向单若商家订单号为空，需要补全商家订单号
            if (OrderTypeEnum.DELIVERY == expressOrderContext.getOrderModel().getOrderType()) {
                expressOrderContext.putExtMaps(ContextInfoEnum.NEED_COMPLEMENT_CUSTOMER_ORDER_NO.getCode(), OrderConstants.YES_VAL);
            }
            // 快运C2C整车直达，补全询价状态为待询价
            if (expressOrderContext.getOrderModel().isFreightC2CFTL()) {
                expressOrderContext.getOrderModel().complement().complementEnquiryStatus(this, EnquiryStatusEnum.WAITE_ENQUIRY);
            }
            // 快运B2C，补全是否发起补签 todo 确认是否放开
            if (expressOrderContext.getOrderModel().isFreightB2C()
                    && StringUtils.isBlank(expressOrderContext.getOrderModel().getAttachment(OrderConstants.RE_SIGN_FLAG))
                    && BatrixSwitch.applyByBoolean(BatrixSwitchKey.INIT_RE_SIGN_FLAG_SWITCH)) {
                expressOrderContext.getOrderModel().putAttachment(OrderConstants.RE_SIGN_FLAG, OrderConstants.INIT_VALUE);
            }
            // 国补审核状态
            if (null != orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.ACTIVATION_CHECK_FREIGHT.getCode())) {
                orderModel.putAttachment(AttachmentKeyEnum.GOV_SUBSIDY_APPROVAL_STATUS.getKey(), OrderConstants.ONE);
                if (null != orderModel.getChannel().getChannelOperateTime()) {
                    orderModel.putAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey(), String.valueOf(orderModel.getChannel().getChannelOperateTime().getTime()));
                } else {
                    orderModel.putAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey(), String.valueOf(System.currentTimeMillis()));
                }
            }
            LOGGER.info("快运接单信息初始化结束");
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("快运接单初始化异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
