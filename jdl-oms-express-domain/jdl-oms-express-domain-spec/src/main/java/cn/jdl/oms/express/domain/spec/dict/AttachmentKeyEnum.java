package cn.jdl.oms.express.domain.spec.dict;

public enum AttachmentKeyEnum {
    JPS("jingPinShi", "是否京品试订单,0：否，1：是"),
    O2O_STORE_CODE("o2oStoreCode", "o2o门店编码"),
    INDUSTRY_CATEGORY("industryCategory", "行业标识"),
    CHANNEL_ORDER_AMOUNT("channelOrderAmount", "渠道订单金额"),
    CUSTOMER_WAREHOUSE_CODE("customerWarehouseCode", "发货仓编码"),
    PLATFORM_TYPE_CODE("platformTypeCode", "平台类型编码"),
    CONTACTLESS_TYPE("contactlessType", "无接触收货方式"),
    ASSIGNED_ADDRESS("assignedAddress", "指定存放地址"),
    PLATFORM_STORE_CODE("platformStoreCode", "平台门店编码"),
    LOGISTICS_AGENT_CODE("logisticsAgentCode", "返佣平台编码"),
    ORDER_SOURCE("orderSource", "订单来源:POP和C2C使用"),
    UNPACKING_INSPECTION("unpackingInspection", "验货方式"),
    SALES_NO("salesNo", "销售员"),
    SALES_SOURCE_TYPE("salesSourceType", "销售员来源"),
    THIRD_PLATFORM_TOKEN("thirdPlatformToken", "微信物流助手订单"),
    FREIGHT_AMOUNT("freightAmount", "运费"),
    VEHICLE_TYPE("vehicleType", "车辆型号"),
    ORDER_STATUS_CUSTOM("orderStatusCustom", "订单自定义状态"),
    HIDDEN_MARK("hiddenMark", "订单隐藏标识"),
    PRINT_STATUS("printStatus", "打印状态"),
    PRINT_OPERATOR("printOperator", "打印人"),
    ROAD_AREA("roadArea", "路区"),
    CANCEL_STATION_NO("cancelStationNo", "取消人所属站点"),
    CANCEL_REASON("cancelReason", "取消原因"),
    CANCEL_REASON_CODE("cancelReasonCode", "取消原因编码"),
    MODIFY_STATION_NO("modifyStationNo", "修改人所属站点"),
    PRODUCT_STRATEGY_CODE("productStrategyCode", "产品策略编码"),
    DELIVERY_BATCH_NUMBER("deliveryBatchNumber", "提货批次号"),
    FROM_VEHICLEID("fromVehicleId", "始发车队"),
    TO_VEHICLEID("toVehicleId", "目的车队"),
    RESERVATION_WAREHOUSE_NO("reservationWarehouseNo", "预约仓单号"),
    PURCHASE_NO("poNo", "采购单号"),
    MODIFY_MARK("modifyMark", "修改信息标记-订单中心内部使用"),
    INPUT_ORDER_TYPE("inputOrderType", "下单方式"),
    REVERSE_EXTEND("reverseExtend", "逆向单扩展字段"),
    JXD_OPERATE_TYPE("jxdOperateType", "京喜达操作类型"),
    BATCH_NO("batchNo", "批次号"),
    SEQUENCE_NO("sequenceNo", "序列号"),
    /**
     * 排序序列号
     */
    ORDER_SORT_NO("orderSortNo", "排序序列号"),
    CARGO_INFOS_KEY("cargoInfos","货品信息变更类型key"),
    PRINT_TIMES("printTimes","打印次数"),
    START_STATION("startStation","始发站点"),
    END_STATION("endStation","始发站点"),
    REMINDER_TIMES("reminderTimes","催单次数"),
    APPOINTMENT_TYPE("appointmentType","预约类型 1.未预约、2.已预约"),
    ISSUE_CALLED("issueCalled","下发已调用"),
    INVOICE_CALLED("invoiceCalled","开票已调用"),
    SINGLE_INSURANCE("singleInsurance","单单保"),
    PRESORT_INTERCEPT_TYPE("presortInterceptType","预分拣拦截类型"),
    CANCEL_REMARK("cancelRemark", "取消备注"),
    PRESORT_EXTEND("presortExtend", "预分拣结果(保存信息更全面)"),
    /** 允许重复 */
    ALLOW_DUPLICATE_KEY("allowedDuplicates", "允许重复"),
    INDIVIDUAL_MS_TYPE("individualMsType", "是否散客挂月结"),
    EXTEND_INFOS("extendInfos", "订单主档，大报文扩展字段"),
    BUSINESS_FLAG("businessFlag", "业务流量标识，用于定义业务的切量逻辑"),
    /**
     * 下单媒介
     * https://joyspace.jd.com/sheets/TvSy8TeUB0NU6YepXjSN [总表]
     */
    ORDER_MEDIUM("orderMedium", "下单媒介"),
    TICKET_NO("ticketNo","优惠券编码"),
    SELF_DEFINE_INFO("selfDefineInfo","商家指定信息"),
    /**
     * 下单人的末级部门编码(目前仅企业寄使用)
     */
    DEPARTMENT_NO("departmentNo", "下单人的末级部门编码"),
    SHIPPER_NO("shipperNo", "承运商编码"),
    SHIPPER_NAME("shipperName", "承运商名称"),
    /**
     * 复重量方(询价)包裹数
     */
    ENQUIRY_QUANTITY("enquiryQuantity", "复重量方(询价)包裹数"),

    /**
     * 售后联系人电话
     */
    SALES_PERSON_PHONE("salespersonPhone", "售后联系人电话"),

    /**
     * 货品信息里的skuInfo
     */
    CARGO_SKU_INFO_EXTEND_PROPS("skuInfo", "货品信息里的skuInfo"),

    /**
     * 特殊显示备注
     */
    REMARK1("remark1", "特殊显示备注"),

    /**
     * 揽收运输方式
     */
    PUP_DELIVERY_TYPE("pupDeliveryType","揽收运输方式"),

    /**
     * 派送运输方式
     */
    DELIVERY_TYPE("deliveryType", "派送运输方式"),

    /**
     * 拓展字段集合
     */
    SHIPMENT_EXTEND_PROPS("shipmentExtendProps", "配送信息扩展字段"),

    /**
     * 超区配送
     */
    OVER_AREA_DISTRIBUTION("overareaDistribution", "超区配送"),

    /**
     * 超区直接配送
     */
    OVER_AREA_DIRECT_DISTRIBUTION("overareaDirectDistribution", "超区直接配送"),

    /**
     * networkType 0：家电网络 1：家居网路
     */
    NET_WORK_TYPE("networkType", "网络类型"),

    /**
     * 0：2C订单 1：2B订单
     */
    LAS_BUSSINESS_MODEL("bussinessModel", "0：2C订单 1：2B订单"),

    /**
     * 货品信息里SKU编码
     */
    PRODUCT_SKU("productSku", "货品信息里SKU编码"),

    WAYBILL_TYPE("waybillType", "运单类型"),
    /**
     * 原收件人地址
     */
    ORIGIN_CONSIGNEE_INFO("originConsigneeInfo", "改址-原收件人信息"),
    /**
     * 原配送信息
     */
    ORIGIN_SHIPMENT_INFO("originShipmentInfo", "改址-原配送信息"),
    /**
     * 原财务信息
     */
    ORIGIN_FINANCE_INFO("originFinanceInfo", "改址-原财务信息"),
    /**
     * 原产品信息
     */
    ORIGIN_PRODUCT_INFO_LIST("originProductInfoList", "改址-原产品信息"),
    /**
     * 业务需要特殊标识的渠道来源
     */
    SYSTEM_CALLER_EXTEND("systemCallerExtend", "业务需要特殊标识的渠道来源"),

    /**
     * 是否已评价
     */
    EVALUATE_STATUS("evaluateStatus", "是否已评价 1、已评价；2、未评价"),
    /**
     * 商家子类别为SOP类型且渠道编码不为售后单时，跳过渠道编码校验。
     * （未满足KA切量，临时提供此字段，后期需废弃，不扩展新的业务场景）
     */
    SKIP_CHECK_SOP_CHANNEL_NO("skipCheckSopChannelNo", "1:跳过商家子类别为SOP类型且渠道编码不为售后单时，跳过渠道编码校验。"),
    /**
     * 客户自定义扩展信息,MAP转JSON
     */
    CUSTOM_EXTEND_MSG("customExtendMsg", "客户自定义扩展信息,MAP转JSON"),

    /**
     * 是否换新单，0：否；1：是
     */
    RENEWED_ORDER("renewedOrder", "是否换新"),
    /**
     * UEP平台账号
     */
    UEP_ACCOUNT_NO("uepAccountNo", "UEP平台账号"),

    /**
     * 异常中心异常码
     */
    EXCEPTION_CENTER_CODE("exceptionCode", "异常中心异常码"),

    /**
     * 整车业务询价单状态
     */
    BUSINESS_EXPANSION_STATUS("businessExpansionStatus", "整车业务询价单状态"),

    BATCH_SEQUENCE_NO("batchSequenceNo", "整车批量序列号"),

    /**
     * 整车是否首个串点（冷链整车业务）
     */
    BATCH_FIRST_ORDER("batchFirstOrder", "整车是否首个串点"),

    /**
     * 整车业务首个串点订单号（支持查询）
     */
    BATCH_FIRST_ORDER_NO("batchFirstOrderNo", "整车业务首个串点订单号"),

    /**
     * 整车业务首个串点客户订单号（支持查询）
     */
    BATCH_FIRST_CUSTOMER_ORDER_NO("batchFirstCustomerOrderNo", "整车业务首个串点客户订单号"),

    /**
     * 车型名称
     */
    VEHICLE_TYPE_NAME("vehicleTypeName", "车型名称"),

    /**
     * 整车订单数
     */
    BATCH_QUANTITY("batchQuantity", "整车订单数"),

    /**
     * 运输erp机构id
     */
    TRANSPORT_ERP_ID("transportErpId", "运输erp机构id"),

    /**
     * 冷链卡班、冷链小票支付方式
     */
    COLD_CHAIN_PAYMENT("coldChainPayment", "冷链卡班、冷链小票支付方式"),
    /**
     * 业务订单来源
     */
    WAYBILL_CHANNEL("waybillChannel", "业务订单来源"),

    /**
     * 业务拓展订单类型
     * 1: 港澳
     */
    BUSSINESS_EXT_ORDER_TYPE("bussinessExtOrderType", "业务拓展订单类型"),

    /**
     * 操作类型
     */
    OPERATE_TYPE("operateType","操作类型"),

    /**
     * 收货平台
     */
    RETURN_PLATFORM("returnPlatform","收货平台"),

    /**
     * 实际妥投时间
     */
    ACTUAL_SIGNED_TIME("actualSignedTime", "实际妥投时间"),

    /**
     * 是否开启二次权益
     */
    INSURANCE_RIGHT("insuranceRight", "是否使用二次权益"),
    /**
     * 是否0元自动写帐
     */
    ZERO_ACCOUNTING("zeroAccounting", "是否0元自动写帐"),
    /**
     * 京东保险是否有可用补运费权益
     */
    HAS_RIGHT("hasInsuranceRight", "京东保险是否有可用补运费权益"),

    /**
     * 保司id
     */
    DEDUCTION_COMPANY_ID("deductionCompanyId", "保司id(结算账号)"),
    /**
     * tms询价失败消息
     */
    TMS_ENQUIRY_FAIL_MSG("tmsEnquiryFailMsg", "tms询价失败消息"),
    /**
     * 询价司机车辆信息
     */
    ENQUIRY_VEHICLE_DRIVER("enquiryVehicleDriver", "询价司机车辆信息"),
    /**
     * 零售pop订单sendPayMap
     */
    SEND_PAY_MAP("sendPayMap", "零售pop订单sendPayMap"),
    /**
     * 快运整车C2C是否需要执行客户确认
     * 0 - 否，接单自动执行客户确认，后续不需要执行客户确认操作
     * 1或者空 - 是（默认），后续需要执行客户确认操作
     */
    NEED_CUSTOMER_CONFIRM("needCustomerConfirm", "快运整车C2C是否需要执行客户确认"),

    ORIGIN_SETTLEMENT_TYPE("originSettlementType", "修改原单结算方式(1：寄付现结,2：到付现结,3：寄付月结)"),
    /**
     * 修改原单支付环节
     * 1：先款支付
     * 2：后款支付
     */
    ORIGIN_PAYMENT_STAGE("originPaymentStage", "修改原单支付环节(1：先款支付 2：后款支付)"),
    /**
     * 是否预售订单
     * 0：否
     * 1：是
     */
    PRE_SALE("preSale", "预售订单"),

    /**
     * 预计拦截地网点id
     */
    PREDICT_INTERCEPT_SITE_ID("predictInterceptSiteId", "预计拦截地网点id"),
    /**
     * 预计拦截地省id
     */
    PREDICT_INTERCEPT_PROVINCE_ID("predictInterceptProvinceId", "预计拦截地省id"),
    /**
     * 预计拦截地市id
     */
    PREDICT_INTERCEPT_CITY_ID("predictInterceptCityId", "预计拦截地市id"),
    /**
     * 预计拦截地县id
     */
    PREDICT_INTERCEPT_COUNTRY_ID("predictInterceptCountryId", "预计拦截地县id"),
    /**
     * 预计拦截地详细地址
     */
    PREDICT_INTERCEPT_ADDRESS("predictInterceptAddress", "预计拦截地详细地址"),
    /**
     * 办理人姓名
     */
    APPLIER_NAME("applierName", "办理人姓名"),
    /**
     * 办理人姓名
     */
    APPLIER_PHONE("applierPhone", "办理人手机"),
    /**
     * 办理人姓名
     */
    APPLY_ID_NO("applyIdNo", "办理证件号"),
    /**
     * 先款是否合并支付标标识
     * 1:是
     * 0:否
     */
    ONLINE_COMBINED_PAY("onlineCombinedPay", "先款合并支付标识"),
    /**
     * 改址多方支付标识，用于做线上兼容逻辑
     * 1:是多发支付
     */
    READDRESS_MULTI_PAY("readdressMultiPay", "改址多方支付标识"),

    /**
     * 原单被合并支付标识
     * 1:是
     * 0:否
     */
    ORIGIN_ONLINE_COMBINED_PAY("originOnlineCombinedPay", "原单被合并支付标识"),
    /**
     * 0：允许
     * 1：禁止
     */
    DISABLE_E_CARD("disableECard", "财务信息扩展字段，禁止使用E卡标识"),
    /**
     * 禁止使用E卡原因编码
     */
    DISABLE_E_CARD_REASON_CODE("disableECardReasonCode", "财务信息扩展字段，禁止使用E卡原因编码"),
    /**
     * 1:是
     */
    TERMINAL_MODIFY_FLAG("terminalModifyFlag", "终端切百川-修改服务切量标识"),
    /**
     * 1:是
     */
    TERMINAL_ENQUIRY_FLAG("terminalEnquiryFlag", "终端切百川-询价服务切量标识"),
    /**
     * 1:是
     */
    PRE_CHECK("preCheck", "前置校验标识"),
    /**
     * 跳过零售店铺校验标识（对比venderId和popId）
     * 1:是
     * 0:否
     */
    SKIP_VALIDATE_VENDER_ID("skipValidateVenderId", "跳过零售店铺校验标识"),

    /**
     * 揽收模式(0-标准价（默认）、1-高铁非标价、2-机场非标价、3-其他)
     */
    COLLECTING_MODE("collectingMode", "终端切百川-询价服务-揽收模式"),

    /**
     * 出库商家ID
     */
    OUTBOUND_VENDER_ID("outboundVenderId", "出库商家ID"),

    /**
     * 成本中心编码
     */
    DEPARTMENT_NAME("departmentName", "成本中心名称"),

    /**
     * POS到付类型（目前仅揽收前记录，用于标识产品互改中是否写过pos到付台账）
     */
    POS_YUN_TYPE("posYunType", "POS到付类型"),

    /**
     * 流程编排特殊流程标识：比如跳过产品中心校验
     */
    SPECIAL_FLOW_FLAGS("specialFlowFlags", "特殊流程标识"),
    /**
     * 售后服务单号，在商品信息扩展字段
     */
    AFTER_SALES_ORDER_NOS("afterSalesOrderNos", "售后服务单号"),
    /**国补审核状态*/
    GOV_SUBSIDY_APPROVAL_STATUS("govSubsidyApprovalStatus", "国补审核状态"),
    GOV_SUBSIDY_STATUS_UPDATE_TIME("govSubsidyStatusUpdateTime", "国补审核状态更新时间"),

    /**
     * 营销折扣信息切换标识
     */
    OPERATION_DISCOUNT("operationDiscountFlag", "营销折扣信息切换标识"),

    /**
     * MaterialTurnoverEnum
     */
    MATERIAL_TURNOVER("materialTurnover", "物资周转"),

    /**
     * 主档扩展字段
     * 是否允许修改称重量方信息
     * 0-否 1-是
     */
    RECHECK_INFO_CHANGE_FLAG("recheckInfoChangeFlag", "主档扩展字段里的是否允许修改称重量方信息标识"),
    /**
     * 主档扩展字段
     * 是否信任托寄物信息
     * 使用场景：由于大件订单，下游是全量覆盖，修改场景需要传特殊标识放开校验
     * 0-否 1-是
     */
    CARGO_INFO_TRUST_FLAG("cargoInfoTrustFlag", "是否信任托寄物信息"),

    /**
     * 拦截场景类型 1-接货退货
     */
    INTERCEPT_SCENARIO("interceptScenario", "拦截场景类型"),

    /**
     * 是否逆向售后新订单 1-是 0-否
     */
    REVERSE_NEW_AFTER_SALES_ORDER("reverseNewAfterSalesOrder", "是否逆向售后新订单"),
    DADA_OP_MODE("dadaOperationMode", "达达流程标识"),
    DADA_ENTRANCE("dadaEntrance", "达达订单下单入口"),
    CREATOR_PHONE("creatorPhone", "下单人人手机号"),
    ORIGIN_ID("originId", "链路标识"),
    /**
     * 最后一次询价成功时间
     */
    LAST_ENQUIRY_SUCCESS_TIME("lastEnquirySuccessTime", "最后一次询价成功时间"),
    /**
     * 询价异常状态
     * EnquiryExceptionStatusEnum
     */
    ENQUIRY_EXCEPTION_STATUS("enquiryExceptionStatus", "询价异常状态"),

    /**
     * 握手交接信息
     */
    HAND_SHAKE_HAND_OVER_INFO("handshakeHandoverInfo", "握手交接信息"),
    ;

    private String key;
    private String desc;
    /**
     * 业务描述
     */
    private String business;

    AttachmentKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    AttachmentKeyEnum(String key, String desc, String business) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
