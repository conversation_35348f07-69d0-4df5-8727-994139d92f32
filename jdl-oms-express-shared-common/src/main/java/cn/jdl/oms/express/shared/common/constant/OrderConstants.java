package cn.jdl.oms.express.shared.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description: 订单常量定义
 * @create 2021-03-29 16:56
 **/
public class OrderConstants {

    /**
     * 英文逗号
     */
    public static final String COMMA = ",";

    /**
     * 是否标识：是
     */
    public static final String YES_VAL = "1";

    /**
     * 是否标识：否
     */
    public static final String NO_VAL = "0";

    /**
     * 隐藏标识
     */
    public static final String HIDDENMARK_CODE = "hiddenMark";
    /**
     * 未删除
     */
    public static final Integer YN_1 = 1;
    /**
     * 删除
     */
    public static final Integer YN_0 = 0;

    public static final String START_STATION = "startStation";

    public static final String END_STATION = "endStation";

    public static final String PRESORT_EXTEND = "presortExtend";

    /**
     * 预分拣时传入的逆向单的原单号key
     */
    public static final String PRESORT_ORIGINAL_WAYBILLNO = "forwardWaybillCode";

    /**
     * (替换customOrderNo) https://logistics-mrd.jd.com/gif?wCode={customOrderNo}
     * 包裹有话说替换字符串
     */
    public static final String ATTACHMENT_URL_REPLACE_STRING = "{customOrderNo}";

    /**
     * 预分拣时传入的逆向单的逆向类型key
     */
    public static final String WAYBILL_REVERSE = "waybillReverse";

    /**
     * 逆向单传入的扩展字段逆向类型key
     */
    public static final String REVERSE_TYPE = "reverseType";

    public static final String CUSTOMER_INFO_EXTEND_PROPS = "customerInfoExtendProps";

    public static final String BUSINESS_STATE = "businessState";

    public static final String JING_PIN_SHI = "jingPinShi";

    /**
     * 揽收的预分拣结果类型
     */
    public static final String PICKUP_PRESORT = "pickupPresort";

    /**
     * 揽收超区原因
     */
    public static final  String BEYOND_MESSAGE_PICK = "beyondMessagePick";

    /**
     * COD修改标识
     */
    public static final String COD_MODIFY_FLAG = "CODModifyFlag";

    /**
     * 派送的预分拣结果类型
     */
    public static final String DELIVERY_PRESORT = "deliveryPresort";

    /**
     * 派送的超区原因
     */
    public static final String BEYOND_MESSAGE_DELIVER = "beyondMessageDeliver";

    /**
     * 邮政限揽超区限制信息
     */
    public static final String SPECIAL_LIMIT_INFO = "specialLimitInfo";

    /**
     * 商家端运单状态
     */
    public static final String SELLER_EXTEND_STATUS = "sellerExtendStatus";
    /**
     * 打印次数
     */
    public static final String PRINT_TIMES = "printTimes";
    /**
     * 打印状态
     */
    public static final String PRINT_STATUS = "printStatus";
    /**
     * 修改次数
     */
    public static final String MODIFY_TIMES = "modifyTimes";
    /**
     * 是否换单
     */
    public static final String NEW_ORDER = "newOrder";
    /**
     * 是否散客挂月结
     */
    public static final String INDIVIDUAL_MS_TYPE = "individualMsType";
    /**
     * 初始值
     */
    public static final String INIT_VALUE = "0";
    /**
     * 已打印
     */
    public static final String PRINTED_VALUE = "1";

    /**
     * 售后取件类型
     */
    public static final String AFTER_SALE_TYPE = "afterSaleType";
    public static final String AFTER_SALE_TYPE_VALUE = "1";

    /**
     * 单单保
     */
    public static final String SINGLE_INSURANCE = "singleInsurance";
    public static final String SINGLE_INSURANCE_VALUE_1 = "1";//是单单保
    public static final String SINGLE_INSURANCE_VALUE_0 = "0";//不是单单保

    /**
     * 销售人员姓名商家联系人
     */
    public static final String SALESPERSON_NAME = "salespersonName";

    /**
     * 运单变更标识-揽收前或揽收后变更
     */
    public static final String IGNORE_MODIFY_FLAG = "ignoreModifyFlag";
    //"0"不忽略修改标识（是否揽收后修改过，是否揽收后改过代收货款，是否揽收后修改过收件人信息），"1"忽略修改标识
    public static final String NOT_IGNORE_MODIFY_FLAG_VALUE = "0";
    public static final String IGNORE_MODIFY_FLAG_VALUE = "1";

    /**
     * 敏感词校验相关定义，如果长度在300以内，敏感词校验模型只需要一次校验即可，如果超过300，数科会截取，因此订单中心不用自己截取
     * 因此可以把多个敏感词拼接成一个，但有个坑，有个坑，有个坑，重要事情说三遍，总长度超过1200，数科不会校验1200后面的
     * 因此拼接后需要判断长度是不是大于1200，
     */
    public static final int SENSITIVE_WORD_MAX_LENGTH = 1200;
    public static final String SENSITIVE_WORD_KEY = "express";
    /**
     * 商品唯一编码
     */
    public static final String GOODS_UNIQUE_CODE = "goodsUniqueCode";

    /**
     * 当前订单序列号
     */
    public static final String SEQUENCE_NO = "sequenceNo";

    /**
     * 商家类型-厂直
     */
    public static final Integer TRADER_MOLD_CHANGZHI = 1010;

    /**
     * 渠道编码为京东平台（0010001）
     */
    public static final String JD_CHANNEL_NO = "0010001";

    /**
     * 交付模式
     */
    public static final String DELIVERY_PATTERN = "deliveryPattern";
    /**
     * 交付模式 为 京仓发货
     */
    public static final String WAREHOUSE_DELIVERY = "1";
    /**
     * 交付模式 为 纯配
     */
    public static final String PURE_DELIVERY = "2";

    /**
     * 行业标识
     */
    public static final String PROFESSION_TYPE = "professionType";

    /**
     * 自提场站ID 来自：https://cf.jd.com/pages/viewpage.action?pageId=109419365
     * 20230714.selfSendUnitId实际为揽收自送场景下的分拣中心id
     */
    public static final String SELF_SEND_UNIT_ID = "selfSendUnitId";

    /**
     * 自送场站ID 来自：https://cf.jd.com/pages/viewpage.action?pageId=109419365
     * 20230714.selfSendUnitId实际为场景下派送自提场景下的分拣中心id
     */
    public static final String SELF_PICKUP_UNIT_ID = "selfPickupUnitId";

    /**
     * 冷链运营模式 来自：https://cf.jd.com/pages/viewpage.action?pageId=109419365
     */
    public static final String COLD_CHAIN_MODEL = "coldChainMold";

    /** 京东快递承运商编码 */
    public static final String JDL_EXPRESS = "2087";

    /** 京东快运承运商编码 */
    public static final String JDL_FREIGHT = "773574";

    /**
     * 补全营业厅标示 1 ： 补全
     */
    public static final String COMPLEMENT_BUSINESS_HALL_FLAG_YES = "1";

    /**
     * 补全营业厅标示 0 或者 null ：不补全，默认
     */
    public static final String COMPLEMENT_BUSINESS_HALL_FLAG_NO = "0";

    /**
     * 扩展字段-扩展信息
     */
    public static final String EXTEND_INFOS = "extendInfos";

    /**
     * 扩展字段-扩展信息-展会名称
     */
    public static final String EXHIBITION_NAME = "exhibitionName";

    /**
     * 揽收站点/车队校验失败拒单标示 1 ： 拒单
     */
    public static final String CHECK_PICKUP_STATION_FLAG_YES = "1";

    /**
     * 揽收站点/车队校验失败拒单标示 0 或者 null ：不拒单，默认
     */
    public static final String CHECK_PICKUP_STATION_FLAG_NO = "0";

    /**
     * 原单是否需要退款：是
     */
    public static final String ORIGINAL_ORDER_NEED_REFUND_FLAG_YES = "1";
    /**
     * 原单是否需要退款：否
     */
    public static final String ORIGINAL_ORDER_NEED_REFUND_FLAG_NO = "0";

    /**
     * 是否信任商家重量体积：是
     */
    public static final String TRUST_CUSTOMER_WEIGHT_VOLUME_YES = "1";

    /**
     * 是否信任商家重量体积：否
     */
    public static final String TRUST_CUSTOMER_WEIGHT_VOLUME_NO = "0";


    /**
     * 来源为SupplyOFC或CLPS的订单且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算
     */
    public static final String DELIVERY_PATTERN_ONE = "1";

    /**
     * deliveryPattern=2
     */
    public static final String DELIVERY_PATTERN_TWO = "2";

    /**
     * 代扣失败/支付失败 接口返回错误码
     */
    public static final String PAYMENT_FAILED_CODE = "paymentFailedCode";

    /**
     * 代扣失败/支付失败 jmq发送交易状态
     */
    public static final String PAYMENT_FAILED_STATUS = "withholdingStatus";

    /**
     * 代扣失败/支付失败 jmq发送交易描述
     */
    public static final String PAYMENT_FAILED_INFO = "paymentFailedInfo";

    /**
     * 财务扩展字段代扣交易payId字段
     */
    public static final String PAYMENT_PAY_ID = "paymentPayId";

    /**
     * 钱包代扣签约协议号
     */
     public static final String PAY_AGREEMENT_NO="walletWithholding";

    /**
     * 钱包代扣账户号
     */
    public static final String PAY_CUSTOMER_NO="walletWithholdingNo";

    /**
     * 钱包代扣账户类型
     */
    public static final String PAY_CUSTOMER_TYPE="walletWithholdingType";
    /**
     * 钱包代扣交易来源
     */
    public static final String TRADE_SOURCE = "tradeSource";

    /**
     * 代扣支付成功状态标识
     */
    public static final String PAY_SUCCESS_STATUS = "1";
    /**
     * 代扣支付失败状态标识
     */
    public static final String PAY_FAIL_STATUS = "2";

    /**
     * 支付处理中的状态标识
     */
    public static final String PAY_ING_STATUS = "3";


    /**
     * 支付来源
     */
    public static final String PAY_BIZ_SOURCE = "jdl-oms-express";

    /**
     * 首单优惠
     */
    public static final String FIRST_ORDER = "FIRST_ORDER";


    /**
     * 起始站点信息
     */
    public static final String startSiteInfo = "startSiteInfo";

    /**
     * 目的站点信息
     */
    public static final String endSiteInfo = "endSiteInfo";

    /**
     * 常量 1
     */
    public static final String ONE = "1";

    public static final String FRESH_GUARANTEE = ONE;

    /**
     * 修改业务流量标识
     * 1：订单中心
     */
    public static final String BUSINESS_FLOW_FLAG = "businessFlowFlag";

    /**
     * 归属的售后服务单号
     */
    public static final String AFTER_SALES_KEY = "afterSalesOrderNos";

    /**
     * 揽收预分拣匹配模式要求
     */
    public static final String REQUIRE_PICKUP_PRESORT_MODE ="requirePickupPresortMode";
    /**
     * 专属小哥
     */
    public static final String SALESPERSON_PIN ="salespersonPin";
    /**
     * 专属小哥类型
     */
    public static final String SALES_TYPE ="salesType";

    /**
     * 抵扣方结算编码
     */
    public static final String DEDUCTION_COMPANY_ID = "deductionCompanyId";
    /**
     * 京享值结算账号
     */
    public static final String JING_XIANG_CODE = "010K318996";
    /**
     * PLUS结算账号
     */
    public static final String PLUS_CODE = "010K319076";
    /**
     * 产品编码
     */
    public static final String PRODUCT_NO = "productNo";
    /**
     * 区域销售erp
     */
    public static final String REGION_SALE_ERP = "regionalSalesErp";

    /**
     * 区域销售name
     */
    public static final String REGION_SALE_NAME = "regionalSalesName";

    /**
     * 修改车型费标识-未修改
     */
    public static final String MODIFY_VEHICLE_FEE_INIT = "0";
    /**
     * 修改车型费标识-销售
     */
    public static final String MODIFY_VEHICLE_FEE_SALE = "1";
    /**
     * 修改车型费标识-系统
     */
    public static final String MODIFY_VEHICLE_FEE_SYSTEM = "2";
    /**
     * 修改车型费标识-营业厅
     */
    public static final String MODIFY_VEHICLE_FEE_BUSINESS_HALL = "3";

    /**
     * 是否创建TMS询价单：是
     */
    public static final String CREATE_TMS_ENQUIRY_BILL_FLAG_YES = "1";

    /**
     * 是否创建TMS询价单：否
     */
    public static final String CREATE_TMS_ENQUIRY_BILL_FLAG_NO = "0";


    /**
     * 修改持久化是否成功：是
     */
    public static final String MODIFY_REPOSITORY_RESULT_SUCCESS = "1";

    /**
     * 改址业务模式 1：一单到底-先付后退
     */
    public static final String READDRESS_MODE_PAY_THEN_REFUND = "1";

    /**
     * 改址业务模式 2：一单到底-多退少补
     */
    public static final String READDRESS_MODE_REFUND_OR_SUPPLEMENT = "2";

    /**
     * 财务信息-扩展字段-改址补款金额
     */
    public static final String READDRESS_SUPPLEMENT_AMOUNT = "readdressSupplementAmount";

    /**
     * 快运整车C2C是否需要执行客户确认
     * 1或者空 - 是（默认），后续需要执行客户确认操作
     * 0 - 否，接单自动执行客户确认，后续不需要执行客户确认操作
     */
    public static final String NEED_CUSTOMER_CONFIRM_NO = "0";

    /**
     * OFC状态码
     */
    public static final String FULFILLMENT_STATUS = "fulfillmentStatus";

    /**
     * 改址次数
     */
    public static final String READDRESS_TIMES = "readdressTimes";

    /**
     * 改址操作模式 1:换单，2:一单到底
     */
    public static final String READDRESS_OPERATE_MODE = "readdressOperateMode";

    public static final Integer READDRESS_TIMES_MAX = 5;

    /**
     * 变更记录写入次数
     */
    public static final String MODIFY_INFO_SEQUENCE = "modifyInfoSequence";


    /**
     * 配送信息-服务要求-邮政限揽校验字段
     */
    public static final String CHECK_FOR_DAWK_DELIVERY = "checkForDawkDelivery";

    /**
     * 邮政限揽校验
     */
    public static final String LIMIT_DAWK_DELIVERY = "1";

    /**
     * 邮政限揽校验toOFC
     */
    public static final String LIMIT_DAWK_DELIVERY_TO_OFC = "0";

    /**
     * 商品信息/货品信息-扩展信息-货品类别
     */
    public static final String CATEGORY = "category";
    /**
     * 改址一单到底支付结果并发处理前缀
     */
    public static final String READDRESS_PAY_RESULT_LOCK_PREFIX = "readdress_pay_result_lock_";

    /**
     * 货品-包装属性
     */
    public static final String PACKAGING_ATTRIBUTES = "packagingAttributes";


    /**
     * 是否强制取消
     */
    public static final String FORCE_CANCEL = "forceCancel";

    /**
     * 1-快运整车不校验状态（除终态），强制取消订单
     */
    public static final String FORCE_CANCEL_YES = "1";

    /**
     * 云仓vmi订单-计费方式
     */
    public static String VMI_BILLING_TYPE = "11";

    /**
     * 商家类型-零售服务内单账号
     */
    public static final Integer TRADER_MOLD_RETAIL_INTERNAL_ORDER_ACCOUNT = 1003;

    /**
     * 商家子类型-云仓VMI
     */
    public static final Integer SUB_TRADER_MOLD_VMI = 100321;


    /**
     * 优惠券-国际业务标识
     */
    public static final String INTL_SOURCE = "Intl";

    /**
     * 溯源码
     */
    public static final String TRACEABILITY_CODE = "traceabilityCode";

    /**
     * 多地址审核状态。1-审批中；2-审批通过；3-取消审批
     */
    public static final String MULTI_ADDRESS_VERIFY_STATUS = "multiAddressVerifyStatus";

    /**
     * 配送-保险承运商类型
     */
    public static final String  INSURANCE_SHIPPER_TYPE = "insuranceShipperType";

    /**
     * COD修改次数
     */
    public static final String COD_MODIFY_TIMES = "codModifyTimes";

    /**
     * COD允许最大修改次数
     */
    public static final Integer COD_MAX_MODIFY_TIMES = 2;

    /**
     * 回传下发ofc标识
     */
    public static final String CALLBACK_ISSUE_FLAG = "callbackIssueFlag";

    /**
     * 复核体积时间
     * "yyyy-MM-dd HH:mm:ss"
     */
    public static final String RECHECK_VOLUME_TIME = "recheckVolumeTime";

    /**
     * 复核重量时间
     * "yyyy-MM-dd HH:mm:ss"
     */
    public static final String RECHECK_WEIGHT_TIME = "recheckWeightTime";

    /**
     * 复核体积/复核重量数据版本号
     * 13位毫秒时间截
     */
    public static final String RECHECK_DATA_VERSION = "recheckDataVersion";

    /**
     * 【非公开，不写入接口文档】内部修改指纹。只用于入参判断，不持久化。指纹算法：先做MD5，再反转
     */
    public static final String INTERNAL_MODIFY_FINGERPRINT = "internalModifyFingerprint";

    /**
     * 零售订单信任标识
     */
    public static final String JD_ORDER_TRUST_FLAG = "jdOrderTrustFlag";

    /**
     * 特殊的台账收入集成省编码
     */
    public static final String ORDER_BANK_PROVINCE_NO = "specialOrderBankProvinceNo";

    /**
     * merchantID
     */
    public static final String MERCHANT_ID = "otsMerchantId";

    /**
     * 主产品变更来源，枚举值：0-无改动；1-终端修改；2-用户修改（预留）
     */
    public static final String MAIN_PRODUCT_CHANGED_SOURCE = "mainProductChangedSource";

    /**
     * 毕业寄
     */
    public static final String ORDER_MEDIUM_NEW_GRADUATION_SEND ="newgraduationsend";

    /**
     * 商家子类型-京喜自营
     */
    public static final Integer SUB_TRADER_MOLD_JINGXIZIYING = 100322;

    /**
     * SKU中ExtTag的Key：supplyInfojxywms
     */
    public static final String SKU_EXTTAG_SUPPLYINFOJXYWMS = "supplyInfojxywms";

    /**
     * SKU中ExtTag的Key：supplyInfojxywms对应含义为【纯配全托】的Value
     *  null和0，1.半托，2.纯配全托，3. 仓配全托
     */
    public static final String SKU_EXTTAG_SUPPLYINFOJXYWMS_EXPRESSQUANTUO = "2";

    /**
     * 合单标识
     * 0：未合单
     * 1：合单成功
     * 2：合单失败
     */
    public static final String ORDER_MERGE_STATUS = "orderMergeStatus";

    /**
     * 是否命中协商再投
     * 1、是；0、否
     */
    public static final String IS_NEGOTIATE_DELIVERY_AGAIN = "isNegotiateDeliveryAgain";

    /**
     * 是否发起补签
     * 1、是；0、否
     */
    public static final String RE_SIGN_FLAG = "reSignFlag";

    /**
     * 运单全程跟踪消息扩展字段-改址模式
     *
     *     改址换单，1
     *     改址一单到底，2
     *     拦截一单到底， 3
     *     拒收一单到底， 4
     */
    public static final String RELOCATION_TYPE = "relocationType";

    /**
     * 自提暂存 台账/写收入集成/开票 运单号前缀
     */
    public static final String SELF_PICKUP_TEMPORARY_STORAGE_PREFIX = "ZTZC";

    /**
     * 产品互改模式: 1-快运改快递，2-快递改快运。保持与运单一致
     */
    public static final String CHANGE_PRODUCT_MODE = "changeProductMode";


    /**
     * 修改systemSubCaller的标识
     */
    public static final String MODIFY_SYSTEM_SUB_CALLER = "modifySystemSubCaller";

    /**
     * 是否允许融合产品互改，1是、0否。支持条件查询
     */
    public static final String ALLOW_UNITED_PRODUCT_EXCHANGE = "allowUnitedProductExchange";

    /**
     * 是否保留原单上的附加费
     */
    public static final String RESERVE_SNAPSHOT_ATTACH_FEES = "reserveSnapshotAttachFees";

    /**
     * 揽收路区
     */
    public static final String START_ROAD_AREA = "startRoadArea";

    /**
     * 派送路区
     */
    public static final String END_ROAD_AREA = "endRoadArea";

    /**
     * 暂存服务单前缀
     */
    public static final String TEMP_STORAGE_FW_ORDER_PREFIX = "ZC";

    /**
     * 报关服务单前缀
     */
    public static final String CUSTOMS_ORDER_PREFIX = "BG";

    /**
     * 询价接口集合类操作字段
     */
    public static final String MODIFIED_FIELDS = "modifiedFields";

    /**
     * 揽收预分拣进人工拒单
     */
    public static final int PRESORT_START_MANUAL_REFUSE = 0b1000;

    /**
     * 派送预分拣进人工拒单
     */
    public static final int PRESORT_END_MANUAL_REFUSE = 0b0010;

    /**
     * 交易用户侧货品名称
     */
    public static final String USER_CARGO_NAME = "userCargoName";

    /**
     * 收件地址类型
     */
    public static final String RECEIVE_ADDRESS_TYPE = "receiveAddressType";

    /**
     * 收获仓编码
     */
    public static final String RECEIVE_NODE_CODE = "receiveNodeCode";

    /**
     * 芝麻代扣信用协议标识
     */
    public static final String ALIPAY_CREDIT_AGREEMENT_ID = "creditAgreementId";

    /**
     * 货物体积最大值 立方厘米
     */
    public static final BigDecimal CARGO_VOLUME_MAX = new BigDecimal("9999000000");

    /**
     * 不进行逆序更新的状态
     */
    public static final String NOT_UPDATE_STATUS = "notUpdateStatus";
    /**
     * 京东转德邦标识
     * 0 - 否 1 - 是
     */
    public static final String JD_TO_DP_FLAG = "jdToDpFlag";

    /**
    商家审核通过状态
     */
    public static final String GOV_SUBSIDY_MERCHANT_APPROVED_STATUS = "7";

    /**
     * 身份证最短的长度
     */
    public static final Integer ID_CARD_LENGTH_OLD = 15;

    /**
     * 身份证最长的长度
     */
    public static final Integer ID_CARD_LENGTH_NEW = 18;

    /**
     * 发货人证件号码的最小长度
     */
    public static final Integer ID_NO_LENGTH_MIN = 6;


    /**
     * 是否送取同步
     * 主要用在修改解除绑定
     * 1:否（默认为空，空即否）
     * 2:是
     */
    public static final String DELIVERY_PICKUP_SYNC_MODIFY_NO = "1";

    /**
     * 是否送取同步
     * 1:否（默认为空，空即否）
     * 2:是
     */
    public static final String DELIVERY_PICKUP_SYNC_YES = "2";

    /**
     * 客服审核状态
     */
    public static final String CSS_AUDIT_STATUS = "cssApprovalStatus";

    /**
     * 0常量
     */
    public static final String ZERO = "0";

    /**
     * 支付单前缀
     */
    public static final String PAY_ORDER_PREFIX = "PAY";

    /**
     * 订单状态切换回去标识（切换为原来映射）
     */
    public static final String EXPRESS_STATUS_BACK_FLAG = "expressStatusBackFlag";

    /**
     * 神行异常恢复请求标识
     */
    public static final String BSC_EXCEPTION_RECOVER_REQUEST_FLAG = "bscExceptionRecoverRequestFlag";
}
